# 2.1 项目整体技术栈

## Monorepo 技术栈

项目采用 Monorepo 架构进行管理，统一了代码规范、构建流程和依赖管理。

| 技术 | 用途 |
|---|---|
| **npm Workspaces** | 管理 `extension` 和 `website` 两个包 |
| **ESLint** | 统一的代码规范检查 |
| **Prettier** | 统一的代码格式化 |
| **TypeScript** | 全项目范围的类型安全 |
| **GitHub Actions** | CI/CD (持续集成/持续部署) |

---

## 🔌 Chrome插件技术栈

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **核心框架** | React + TypeScript | 18 / 5 | 组件化开发，类型安全，提升代码质量和可维护性。 |
| **构建工具** | Vite + @crxjs/vite-plugin | 5 / 2 | 极速热更新和构建，专为Chrome插件（MV3）优化的自动化打包。 |
| **样式系统** | Tailwind CSS | 3 | 原子化CSS，提供极高的开发效率和统一的设计语言。 |
| **UI组件库** | shadcn/ui (基于 Radix UI) | - | 轻量、高可定制、现代美观，完美适配插件的小窗口UI。 |
| **状态管理** | Zustand | 4 | 简洁、轻量、无模板代码，非常适合插件的弹窗和后台状态通信。 |
| **路由管理** | React Router | 6 | 用于��现插件内部页面（如Popup和Options页面）的导航。 |
| **测试框架** | Jest + React Testing Library | 29 | 提供完整的单元测试和组件测试覆盖，保证代码质量。 |

---

## 🌐 官方网站技术栈

| 技术层级 | 技术选型 | 版本 | 优势与用途 |
|---|---|---|---|
| **全栈框架** | Next.js (App Router) | 14+ | 业界领先的React框架，支持SSR/SSG，提供优秀的性能和开发体验。 |
| **后端服务** | Supabase | - | 提供数据库（PostgreSQL）、用户认证、存储等一体化后端服务，简化开发。 |
| **支付系统** | Stripe | - | 全球领先的在线支付解决方案，用于处理用户订阅和计费。 |
| **样式系统** | Tailwind CSS + shadcn/ui | 3 / - | 与Chrome插件保持设计风格的完全统一。 |
| **动画库** | Framer Motion | 10+ | 为网站添加流畅、现代的交互动画，提升用户体验。 |
| **部署平台** | Vercel | - | 与Next.js无缝集成，提供零配置、全球CDN加速的自动化部署。 |
