## 第一周冲刺
### 20250713
- [ ]  速览Chrome插件相关知识点，对整体设计，可以规划大纲
- [ ]  
### 20250714
- [ ]  content完成deepseek页面注入，会出现浮动气泡，
- [ ]  当聚焦到输入框时，小球移动到当输入框上方，
- [ ]  并且能实现在输入框的发送按钮左边，增加一个“存档”按钮，作用是点击后，提示词存入小球（即storage）,
- [ ]  要有一个飞入动画，存储成功后，小球晃动表示成功（后续优化为飞入个小球放大，表示吃到的效果）


### 20250715
- [ ] 鼠标悬浮到小球上后，小球上方出现一堆聊天气泡，为历史提示词记录
- [ ] 提示词旁边要求已经在对应网站，发送过的网站icon

### 20250716 
- [ ] deepseek网站，关于问题的回答，也存入storage

### 20250717
- [ ] 研究如何使用subbase数据库托管和vercel托管服务 

### 20250718 

### 20250719 

## 第二周发布
- [ ] 发布第一版插件
- [ ] 收集用户反馈
- [ ] 优化插件功能
- [ ] 内测版所有用户免费获得pro功能，即无限的提示词网络同步，免费版只提供本地的同步功能。
- [ ] 后续想开发android app，提供历史记录的查看，以及跳转对应app功能，负责给 deepseek软件引流
- [ ] 后续提供deepseek页面未同步过的页面的同步功能。
