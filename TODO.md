# DeepSeek 页面注入功能开发任务

## 需求分析
根据 `docs/需求.md` 的要求，需要实现以下功能：
1. content script 完成 DeepSeek 页面注入，会出现浮动气泡
2. 当聚焦到输入框时，小球移动到输入框上方
3. 在输入框的发送按钮左边，增加一个"存档"按钮，点击后提示词存入小球（即storage）

## 技术分析
通过分析 DeepSeek 页面源码，发现：
- 输入框：`textarea` 元素，可能的选择器包括 `textarea[placeholder*="输入"]`
- 发送按钮：位于输入框右侧的按钮元素
- 页面使用了复杂的 CSS 类名和结构

## 任务分解

### [x] 任务1：创建 DeepSeek 适配器
- [x] 1.1 在 `extension/src/content/adapters/` 目录下创建 `deepseek-adapter.ts`
- [x] 1.2 实现 DeepSeek 页面的元素选择器识别
- [x] 1.3 定义输入框、发送按钮等关键元素的选择器

### [x] 任务2：实现浮动气泡组件
- [x] 2.1 创建浮动气泡组件（使用原生DOM）
- [x] 2.2 实现气泡的基本样式和动画
- [ ] 2.3 添加气泡的拖拽功能（可选）

### [x] 任务3：实现输入框聚焦检测
- [x] 3.1 监听输入框的 focus 和 blur 事件
- [x] 3.2 实现气泡移动到输入框上方的动画
- [ ] 3.3 处理页面滚动时气泡位置的更新

### [x] 任务4：实现存档按钮
- [x] 4.1 在发送按钮左侧插入存档按钮
- [x] 4.2 设计存档按钮的样式，与页面风格保持一致
- [x] 4.3 实现按钮的点击事件处理

### [x] 任务5：实现提示词存储功能
- [x] 5.1 获取输入框中的文本内容
- [x] 5.2 调用 storage API 保存提示词
- [x] 5.3 添加保存成功的视觉反馈

### [x] 任务6：集成到 content script
- [x] 6.1 修改 `extension/src/content/index.ts`
- [x] 6.2 添加 DeepSeek 域名检测
- [x] 6.3 初始化 DeepSeek 适配器

### [/] 任务7：测试和优化
- [x] 7.1 构建项目成功
- [ ] 7.2 在 DeepSeek 页面测试功能
- [ ] 7.3 处理页面动态加载的情况
- [ ] 7.4 优化性能和用户体验

## 开发优先级
1. 高优先级：任务1、任务6（基础架构）
2. 中优先级：任务2、任务3（核心功能）
3. 低优先级：任务4、任务5（存档功能）
4. 测试优先级：任务7（测试优化）

## 技术要点
- 使用 MutationObserver 监听页面动态变化
- 使用 CSS-in-JS 或 styled-components 确保样式不冲突
- 使用 Chrome Extension Storage API 进行数据持久化
- 考虑页面的 CSP（内容安全策略）限制

## 预期完成时间
- 任务1-2：2小时
- 任务3-4：3小时
- 任务5-6：2小时
- 任务7：1小时
- 总计：8小时

## 已完成功能总结

### ✅ 核心功能实现
1. **浮动气泡组件**
   - 创建了一个美观的圆形浮动气泡，位于页面右上角
   - 使用渐变背景和阴影效果，提供良好的视觉体验
   - 支持悬停动画效果（缩放和阴影变化）

2. **输入框聚焦检测**
   - 监听 DeepSeek 输入框的 focus 和 blur 事件
   - 当聚焦时，气泡平滑移动到输入框上方居中位置
   - 失焦时，气泡返回默认的右上角位置

3. **存档按钮**
   - 在发送按钮左侧添加了存档按钮
   - 使用文档图标，样式与页面保持一致
   - 支持悬停效果和点击反馈

4. **提示词存储功能**
   - 点击存档按钮可将当前输入框内容保存到 Chrome Storage
   - 支持最多存储100条提示词，超出时自动删除最旧的
   - 提供存档成功的视觉反馈和飞行动画

5. **用户体验优化**
   - 添加了多种通知类型（成功、错误、信息）
   - 实现了存档按钮到气泡的飞行动画效果
   - 所有交互都有适当的视觉反馈

### 🔧 技术实现亮点
- 使用 TypeScript 确保类型安全
- 采用 MutationObserver 监听页面动态变化
- 使用 Chrome Extension Storage API 进行数据持久化
- 实现了平滑的 CSS 动画和过渡效果
- 考虑了 z-index 层级避免与页面元素冲突

### 🔧 最新改进（针对气泡移动问题）

1. **增强输入框检测**
   - 扩展了输入框选择器，支持更多类型的输入元素
   - 使用事件委托监听 `focusin` 和 `focusout` 事件
   - 添加了 MutationObserver 监听页面动态变化

2. **改进气泡移动逻辑**
   - 添加了边界检查，确保气泡不会移出视口
   - 增加了详细的调试日志
   - 添加了聚焦时的缩放效果

3. **调试功能**
   - 右键点击气泡可输出调试信息
   - 可以手动测试气泡移动功能
   - 显示所有找到的输入元素

### 📋 测试指南

**在 DeepSeek 页面测试步骤：**

1. **加载扩展**
   - 将 `extension/dist` 目录加载到 Chrome 扩展程序
   - 访问 https://chat.deepseek.com

2. **验证基础功能**
   - [ ] 确认右上角出现紫色渐变的浮动气泡
   - [ ] 点击气泡查看存档提示词功能

3. **测试气泡移动功能**
   - [ ] 点击输入框，观察气泡是否移动到输入框上方
   - [ ] 点击页面其他地方，观察气泡是否返回右上角
   - [ ] 右键点击气泡查看调试信息（控制台）

4. **测试存档功能**
   - [ ] 在输入框输入文字
   - [ ] 查看发送按钮左侧是否出现存档按钮
   - [ ] 点击存档按钮测试保存功能

5. **调试方法**
   - 打开浏览器开发者工具（F12）
   - 查看控制台日志了解功能执行情况
   - 右键点击气泡获取详细调试信息
