<!DOCTYPE html>
<!-- saved from url=(0071)https://chat.deepseek.com/a/chat/s/41859549-b26f-4db7-9cf8-db661db6a4ea -->
<html lang="en" class="notranslate" translate="no"><plasmo-csui><template shadowrootmode="open"><div id="plasmo-shadow-container" style="z-index: 2147483647; position: relative;"><div id="plasmo-overlay-0" class="plasmo-csui-container" style="display: flex; position: absolute; top: 0px; left: 0px;"></div></div></template></plasmo-csui><plasmo-csui><template shadowrootmode="open"><style></style><div id="plasmo-shadow-container" style="z-index: 2147483647; position: relative;"><div id="plasmo-overlay-0" class="plasmo-csui-container" style="display: flex; position: absolute; top: 0px; left: 0px;"></div></div></template></plasmo-csui><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek</title><meta name="viewport" content="initial-scale=1.0,maximum-scale=1,width=device-width,viewport-fit=cover"><meta name="commit-id" content="e3c30c6"><meta name="description" content="Chat with DeepSeek AI – your intelligent assistant for coding, content creation, file reading, and more. Upload documents, engage in long-context conversations, and get expert help in AI, natural language processing, and beyond. | 深度求索（DeepSeek）助力编程代码开发、创意写作、文件处理等任务，支持文件上传及长文本对话，随时为您提供高效的AI支持。"><meta name="keywords" content="DeepSeek,DeepSeek AI,DeepSeek Chat,AI assistant,coding assistant,long-context LLM,Open Source LLM,natural language processing,AI chatbot,AI long text,AI content creation,large language model,深度求索,大语言模型,自然语言处理,AI聊天机器人,代码开发,创意写作,AI助手,长文本对话,文件处理AI,大模型公司,人工智能,AI开发助手"><meta property="og:url" content="https://chat.deepseek.com"><meta property="og:type" content="website"><meta property="og:title" content="DeepSeek"><meta property="og:description" content="Chat with DeepSeek AI."><meta property="og:image" content="https://cdn.deepseek.com/images/deepseek-chat-open-graph-image.jpeg"><meta name="twitter:card" content="summary_large_image"><meta property="twitter:domain" content="deepseek.com"><meta property="twitter:url" content="https://chat.deepseek.com"><meta name="twitter:title" content="DeepSeek"><meta name="twitter:description" content="Chat with DeepSeek AI."><meta name="twitter:image" content="https://cdn.deepseek.com/images/deepseek-chat-open-graph-image.jpeg"><meta name="google" content="notranslate"><meta name="ip" content="**************"><meta name="region" content="CN"><link rel="preconnect" href="https://fonts.googleapis.com/"><link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin=""><link rel="apple-touch-icon" href="https://cdn.deepseek.com/chat/icon.png"><link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500&amp;display=swap" rel="stylesheet" media="print" onload="this.media=&quot;all&quot;"><link rel="icon" type="image/x-icon" href="https://chat.deepseek.com/favicon.svg"><script type="text/javascript" async="" id="_intercom_npm_loader" src="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/guh50jw4"></script><script>!function(n,t){if(n.LogAnalyticsObject=t,!n[t]){var c=function(){c.q.push(arguments)};c.q=c.q||[],n[t]=c}n[t].l=+new Date}(window,"collectEvent")</script><script async="" src="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/collect-rangers-v5.2.1.js"></script><script defer="" src="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/default-vendors.57646c6db5.js"></script><script defer="" src="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/main.9dfb508c83.js"></script><link href="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/main.f704369816.css" rel="stylesheet"><style>
body.change-theme * {
  transition: none !important;
}

body.change-theme *::before {
  transition: none !important;
}

body.change-theme *::after {
  transition: none !important;
}
</style><link data-webpack="@deepseek/chat:chunk-945" rel="stylesheet" href="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/katex.66a418a9b7.css"><script data-smcp="true" src="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/smcp.min.js"></script><script charset="utf-8" src="chrome-extension://jgphnjokjhjlcnnajmfjlacjnjkhleah/js/btype.js"></script><script type="text/javascript" charset="utf-8" src="chrome-extension://jgphnjokjhjlcnnajmfjlacjnjkhleah/js/chrome.js"></script><style>@font-face {
  font-family: 'quote-cjk-patch';
  src: local('PingFangSC-Regular');
  unicode-range: U+201C-201D, U+2018-2019;
}</style><style type="text/css">.gwd-row {
  display: flex;
  flex-direction: row;
}
.gwd-inline-row {
  display: inline-flex;
  flex-direction: row;
}
.gwd-column {
  display: flex;
  flex-direction: column;
}
.gwd-inline-column {
  display: inline-flex;
  flex-direction: column;
}
.gwd-align {
  align-content: center;
  align-items: center;
}
.gwd-jcc {
  justify-content: center;
}
.gwd-jic {
  justify-items: center;
}
.gwd-button {
  outline: none;
  border: none;
}
.bjg-bar-button {
  font-size: 0;
}
.bjg-hover-bg {
  background: #fffbef;
}
.bjg-bar-button:hover {
  background: #fffbef;
  cursor: pointer;
}
.bjg-bar-button:hover .bjg-window {
  display: block;
}
.mainbar-fold .bjg-bar-button,
.mainbar-fold #top_coupon_btn,
.mainbar-fold .rinfo-btn,
.mainbar-fold .gwd-bottom-tmall {
  display: none!important;
}
.gwd-font12 {
  font-size: 12px;
}
.gwd-font14 {
  font-size: 14px;
}
.gwd-red {
  color: #ff3532;
}
.gwd-red-bg {
  background: #ff3532;
}
.gwd-hui333 {
  color: #333333;
}
.gwd-hui999 {
  color: #999999;
}
.gwd-font10 {
  font-size: 12px;
  transform: scale(0.8333);
  transform-origin: bottom center;
}
.gwd-font11 {
  font-size: 12px;
  transform: scale(0.91666);
  transform-origin: bottom center;
}
.gwd-font9 {
  font-size: 12px;
  transform: scale(0.75);
  transform-origin: bottom center;
}
.gwd-hoverable:hover {
  background: #edf1f2;
}
.right-info > * {
  border-left: 1px solid #edf1f2;
}
.gwd-red-after-visit:hover {
  color: #e03024 !important;
}
.gwd-button:hover {
  filter: brightness(1.1);
}
.gwd-button {
  padding-top: 1px;
  padding-bottom: 1px;
}
.gwd-button:active {
  filter: brightness(0.9);
}
.gwd-fadeout-5s {
  opacity: 0;
  transition: opacity 5s;
}
.gwd-scrollbar::-webkit-scrollbar {
  width: 6px;
  border-radius: 17px;
}
.gwd-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 17px;
  background: #999;
}
#gwdang_main,
.gwdang-main,
.bjgext-detail {
  font-size: 12px;
}
#gwdang_main button,
.gwdang-main button,
.bjgext-detail button {
  text-align: center;
}
.gwd-width-100 {
  width: 100%;
}
.gwd-overlay {
  font-family: "Microsoft YaHei", "Arial", "SimSun", serif;
  font-size: 0;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.35);
  z-index: 999999999;
}
.gwd-font-pfm {
  font-family: 'PingFangSC-Medium';
  font-weight: normal!important;
}
@font-face {
  font-family: 'PingFangSC-Medium';
  src: local('PingFangSC-Medium');
}
.gwd-font-pfm {
  font-family: local('PingFangSC-Medium'), system-ui;
  font-weight: bold;
}
#gwd_minibar svg,
.gwdang-main svg,
#bjgext_mb_bg svg,
#bjgext_mainbar svg {
  fill: transparent;
}
.gwd-common-font {
  font-family: 'PingFang SC', 'Microsoft YaHei', '\5FAE\8F6F\96C5\9ED1', 'Hiragino Sans GB', 'WenQuanYi Micro Hei';
}
</style><style type="text/css">.gwd-row[data-v-07d1070f] {
  display: flex;
  flex-direction: row;
}
.gwd-inline-row[data-v-07d1070f] {
  display: inline-flex;
  flex-direction: row;
}
.gwd-column[data-v-07d1070f] {
  display: flex;
  flex-direction: column;
}
.gwd-inline-column[data-v-07d1070f] {
  display: inline-flex;
  flex-direction: column;
}
.gwd-align[data-v-07d1070f] {
  align-content: center;
  align-items: center;
}
.gwd-jcc[data-v-07d1070f] {
  justify-content: center;
}
.gwd-jic[data-v-07d1070f] {
  justify-items: center;
}
.gwd-button[data-v-07d1070f] {
  outline: none;
  border: none;
}
.bjg-bar-button[data-v-07d1070f] {
  font-size: 0;
}
.bjg-hover-bg[data-v-07d1070f] {
  background: #fffbef;
}
.bjg-bar-button[data-v-07d1070f]:hover {
  background: #fffbef;
  cursor: pointer;
}
.bjg-bar-button:hover .bjg-window[data-v-07d1070f] {
  display: block;
}
.mainbar-fold .bjg-bar-button[data-v-07d1070f],
.mainbar-fold #top_coupon_btn[data-v-07d1070f],
.mainbar-fold .rinfo-btn[data-v-07d1070f],
.mainbar-fold .gwd-bottom-tmall[data-v-07d1070f] {
  display: none!important;
}
.gwd-font12[data-v-07d1070f] {
  font-size: 12px;
}
.gwd-font14[data-v-07d1070f] {
  font-size: 14px;
}
.gwd-red[data-v-07d1070f] {
  color: #ff3532;
}
.gwd-red-bg[data-v-07d1070f] {
  background: #ff3532;
}
.gwd-hui333[data-v-07d1070f] {
  color: #333333;
}
.gwd-hui999[data-v-07d1070f] {
  color: #999999;
}
.gwd-font10[data-v-07d1070f] {
  font-size: 12px;
  transform: scale(0.8333);
  transform-origin: bottom center;
}
.gwd-font11[data-v-07d1070f] {
  font-size: 12px;
  transform: scale(0.91666);
  transform-origin: bottom center;
}
.gwd-font9[data-v-07d1070f] {
  font-size: 12px;
  transform: scale(0.75);
  transform-origin: bottom center;
}
.gwd-hoverable[data-v-07d1070f]:hover {
  background: #edf1f2;
}
.right-info > *[data-v-07d1070f] {
  border-left: 1px solid #edf1f2;
}
.gwd-red-after-visit[data-v-07d1070f]:hover {
  color: #e03024 !important;
}
.gwd-button[data-v-07d1070f]:hover {
  filter: brightness(1.1);
}
.gwd-button[data-v-07d1070f] {
  padding-top: 1px;
  padding-bottom: 1px;
}
.gwd-button[data-v-07d1070f]:active {
  filter: brightness(0.9);
}
.gwd-fadeout-5s[data-v-07d1070f] {
  opacity: 0;
  transition: opacity 5s;
}
.gwd-scrollbar[data-v-07d1070f]::-webkit-scrollbar {
  width: 6px;
  border-radius: 17px;
}
.gwd-scrollbar[data-v-07d1070f]::-webkit-scrollbar-thumb {
  border-radius: 17px;
  background: #999;
}
#gwdang_main[data-v-07d1070f],
.gwdang-main[data-v-07d1070f],
.bjgext-detail[data-v-07d1070f] {
  font-size: 12px;
}
#gwdang_main button[data-v-07d1070f],
.gwdang-main button[data-v-07d1070f],
.bjgext-detail button[data-v-07d1070f] {
  text-align: center;
}
.gwd-width-100[data-v-07d1070f] {
  width: 100%;
}
.gwd-overlay[data-v-07d1070f] {
  font-family: "Microsoft YaHei", "Arial", "SimSun", serif;
  font-size: 0;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.35);
  z-index: 999999999;
}
.gwd-font-pfm[data-v-07d1070f] {
  font-family: 'PingFangSC-Medium';
  font-weight: normal!important;
}
@font-face {
  font-family: 'PingFangSC-Medium';
  src: local('PingFangSC-Medium');
}
.gwd-font-pfm[data-v-07d1070f] {
  font-family: local('PingFangSC-Medium'), system-ui;
  font-weight: bold;
}
#gwd_minibar svg[data-v-07d1070f],
.gwdang-main svg[data-v-07d1070f],
#bjgext_mb_bg svg[data-v-07d1070f],
#bjgext_mainbar svg[data-v-07d1070f] {
  fill: transparent;
}
.gwd-common-font[data-v-07d1070f] {
  font-family: 'PingFang SC', 'Microsoft YaHei', '\5FAE\8F6F\96C5\9ED1', 'Hiragino Sans GB', 'WenQuanYi Micro Hei';
}
.gwd-taobao[data-v-07d1070f] {
  margin-top: 20px;
}
.gwd-taobao span[data-v-07d1070f] {
  color: #ff4400;
}
.gwd-jd[data-v-07d1070f] {
  margin-top: 20px;
}
.gwd-jd span[data-v-07d1070f] {
  color: #e2231a;
}
a[data-v-07d1070f] {
  white-space: nowrap;
  position: relative;
  height: 24px;
  font-family: 'Microsoft YaHei';
}
a[data-v-07d1070f]:hover {
  text-decoration: none;
  cursor: pointer;
}
a:hover .gwd-tooltip[data-v-07d1070f] {
  display: block;
}
.gwd-coupon[data-v-07d1070f] {
  height: 24px;
  box-sizing: border-box;
  min-width: 106px;
}
.gwd-coupon[data-v-07d1070f] {
  background-size: contain;
  padding-left: 8px;
  padding-right: 8px;
}
.gwd-coupon[data-v-07d1070f]:before,
.gwd-coupon[data-v-07d1070f]:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 9px;
  background-size: contain;
}
.gwd-coupon[data-v-07d1070f]:before {
  left: 0;
}
.gwd-coupon[data-v-07d1070f]:after {
  right: 0;
  transform: rotate(180deg);
}
.gwd-coupon.gwd-taobao[data-v-07d1070f] {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAA0CAYAAAAjfRLqAAAAAXNSR0IArs4c6QAAAU5JREFUeF7tlNENgDAQhdrd3H+lc4cmGgX8r5YHca+eFgAtsOdaA+IJRb5AQcsDoOHvmekPTbMq5ilosXwiekETrYqZClosn4he0ESrYqaCFssnohc00aqYqaDF8onoBU20KmYqaLF8InpBE62KmQpaLJ+IXtBEq2KmghbLJ6IXNNGqmKmgxfKJ6AVNtCpmKmixfCJ6QROtipkKWiyfiF7QRKtipoIWyyeiFzTRqpipoMXyiegFTbQqZiposXwiekETrYqZClosn4he0ESrYqaCFssnohc00aqYqaDF8onoBU20KmYqaLF8InpBE62KmQpaLJ+IXtBEq2KmghbLJ6IXNNGqmKmgxfKJ6AVNtCpmKmixfCJ6QROtipkKWiyfiF7QRKtipoL+m/yZv9341fvuuVYLvTp5H3tygYJ+ct3zd/eTOdxuH57rWAt8coEbbyjDgopRg1YAAAAASUVORK5CYII=);
}
.gwd-coupon.gwd-taobao[data-v-07d1070f]:before,
.gwd-coupon.gwd-taobao[data-v-07d1070f]:after {
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAA0CAYAAACHO2h8AAAAAXNSR0IArs4c6QAAAZJJREFUSEvtlz9OAkEUh79NsNJiOy038QDuEUggxM6INWCrDRRSwwE0UtqtiR7A3mKPwBH2CF7AZM2bAeJkIeyb3U004TU0w5f3Z37vNxtQUwTCybvkVXkNgT79E3MzumjDQwJnkbrSYmknITymcB6rYAZkGt4m4og5OSMkM4EpYgNawUJaZEDIewan5Ut0QKurIKm0TUaSWcnYBloCcSVQ3mFMwMKUJKUpwk6tQ0KANMTWMn2F3kiBAXf8xyEMZ9CfqCBy2AV9fIHAPMIF1SYR5ch/J16UiPTn7lld3Bok0ogIuDK3ejiHwUwFcyXSNeNPEeG+ZZjfkrH7Zr8sVRugGa3ll0R8I1rzU7/ZRS0jERlXTO8WpknJ7thjxfGLYJ9S1S4qgmTkNxMvmTQkkdq0drCjbResGYmsfO0v2pFsSKWTFLUmdjSYqwT7H+xI9rTHa237PqrJjuzL4X4B12NVww92tL9d9maLHYmXtRAPir2fx85HTS121B9bsSre1+uiG7Kj/T3deaLe77UKiWz++gOA98aFVwFZGwAAAABJRU5ErkJggg==);
}
.gwd-coupon.gwd-taobao[data-v-07d1070f]:hover {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAA0CAYAAAAjfRLqAAAAAXNSR0IArs4c6QAAAVNJREFUeF7tlMENgzAUxZLd2H+NXrvB7w6RQMU290D8bLFXTwuAFthzrQHxhCJfoKDlAdDw93w//aFpVsU8BS2WT0QvaKJVMVNBi+UT0QuaaFXMVNBi+UT0giZaFTMVtFg+Eb2giVbFTAUtlk9EL2iiVTFTQYvlE9ELmmhVzFTQYvlE9IImWhUzFbRYPhG9oIlWxUwFLZZPRC9oolUxU0GL5RPRC5poVcxU0GL5RPSCJloVMxW0WD4RvaCJVsVMBS2WT0QvaKJVMVNBi+UT0QuaaFXMVNBi+UT0giZaFTMVtFg+Eb2giVbFTAUtlk9EL2iiVTFTQYvlE9ELmmhVzFTQYvlE9IImWhUzFbRYPhG9oIlWxUwFLZZPRC9oolUxU0GL5RPRC5poVcxU0G+TP/O2Gz963z3XaqFHJ+9jdy5Q0Heue/7ufjKH2+3Dcx1rgb9c4AdcEryACPINxAAAAABJRU5ErkJggg==);
}
.gwd-coupon.gwd-taobao[data-v-07d1070f]:hover:before,
.gwd-coupon.gwd-taobao[data-v-07d1070f]:hover:after {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAA0CAYAAACHO2h8AAAAAXNSR0IArs4c6QAAAYtJREFUSEvtl79Kw2AUxX+BuCiULroVstfBVadIOwgd3NqxOOii4GAfwDoLdqpjcVdfwKF7HZydHH0JIXJvk+pHWujNH6nQC9mSk/Pde849iUdB5QlO1CTKi1cS0ONnZmIuo/oBnA9gp2YGTB9tqwL9Jwh2TWAKpA0PCdjgmoguwkzADDUDisGq+HwAVYYT0xEdoFgKYyBURsJsyZoH9Abs5QKKGlziMWC7BveTJblMb5tOrcEIj0CPJCUSOOxkAEosslmBTg9apyaQH0YJ0MM7iI4ylCvIwixiHPlv4mmLSH9ObsyHS4DEGgEex6rq9hW0eyYw1yJNHf9YGz58NTV+sbJvX0wboByvRUcEfCFey+Z+3UW+WuRODRt24GJgb7aTImJY0ZNx3bo6krG3zkzTSmiXZJHCvLaOo3kCK8cica6tYhzJhjQmSXrVZtiO/yGOjJ8yfxJHXX1L1qklFKN1HC10v8aRZJnPSOOovg/951WIIzGqXMZMS3utsBQxdcW9udj/tRxEZo9+AyVqv4MZSS3ZAAAAAElFTkSuQmCC);
}
.gwd-coupon.gwd-jd[data-v-07d1070f] {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKoAAAAsCAYAAAD1nyNHAAAAAXNSR0IArs4c6QAAAR1JREFUeF7tnbERgFAMhcz+2+k0VnGF33kcWFvwAmfrXD1dAHCBee9nAZwhyi9QqPIAKPNnd/uiUmyJOQtVLJ80vVBJtsSshSqWT5peqCRbYtZCFcsnTS9Uki0xa6GK5ZOmFyrJlpi1UMXySdMLlWRLzFqoYvmk6YVKsiVmLVSxfNL0QiXZErMWqlg+aXqhkmyJWQtVLJ80vVBJtsSshSqWT5peqCRbYtZCFcsnTS9Uki0xa6GK5ZOmFyrJlpi1UMXySdMLlWRLzFqoYvmk6YVKsiVmLVSxfNL0QiXZErMWqlg+aXqhkmyJWQtVLJ80vVBJtsSshSqWT5peqCRbYtZCFcsnTe/3PSRbYtZC/V9+v086cDAH7/RKF/j9Ah900KcI8oZhhwAAAABJRU5ErkJggg==);
}
.gwd-coupon.gwd-jd[data-v-07d1070f]:before,
.gwd-coupon.gwd-jd[data-v-07d1070f]:after {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAsCAYAAABovuiKAAAAAXNSR0IArs4c6QAAAdVJREFUSEvNVlFO20AQfbOzayEhUUepgkIicFBKCmoljpAb0CNwA7gBuUG4AUfhCCvb6jdH4DPFldhqXCWya/Exi5G6n5b9/ObNmzdL6OmQ4LwUZXgv3scAudMZaH8/ilyHEX8egsdjNVgNtMnzFUCZAa5AlPJoBD4cqcBqoO3Z+HJpGI+UOLjFIh6o2cHk+7d4oMr7y8DWwzkkXyMYbYrimog+UcAKQCr6iE6a0+matF9soD0tIHs6g+nDR1qBm6xbjMwgrbWhJNFWhq5GiYOdzdRgO0NuvM+Y7UMAljGCt5wdvE8rts9gRnJxriqvBdSbs3+XP+9ew+squrSqKB8DKANCJqz45Bh8cKAvbRe1xoAnR+A0VYHIy63292ZItzhT+2dLvRu1Eem4K60qigeJWjGjPLTTCcxgoNKp5aNfeX5LZNbR7W/+ettBrfD/hL/PDNun6BGRgQWQsXXrEMKlxImdTvUatXa/c3XUajOp1X4JfTMcgphVbD7O2TH+edPZsq7tfK4u7+8lQnY+QgqDNYiyd18idiv7v4naKi9vAuGe9vbgvsxVFqg1kukPIInFH3XUTifgmOlvOjvmJtIxpLs4V7f9TR+phGm83O89O5ZF87s/IaG3Cf8Fdl8AAAAASUVORK5CYII=);
  background-size: cover;
  background-repeat: no-repeat;
}
.gwd-coupon.gwd-jd[data-v-07d1070f]:hover {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKoAAAAsCAYAAAD1nyNHAAAAAXNSR0IArs4c6QAAAR9JREFUeF7tnbENgFAQhbz9t9NdTGzUFX73Q8DagncQW+fo6QKAC8xzXh+AM0T5BQpVHgBl/rz33ReVYkvMWahi+aTphUqyJWYtVLF80vRCJdkSsxaqWD5peqGSbIlZC1UsnzS9UEm2xKyFKpZPml6oJFti1kIVyydNL1SSLTFroYrlk6YXKsmWmLVQxfJJ0wuVZEvMWqhi+aTphUqyJWYtVLF80vRCJdkSsxaqWD5peqGSbIlZC1UsnzS9UEm2xKyFKpZPml6oJFti1kIVyydNL1SSLTFroYrlk6YXKsmWmLVQxfJJ0wuVZEvMWqhi+aTphUqyJWYtVLF80vRCJdkSsxaqWD5per/vIdkSsxbqfvn9PmnBwSy80ytdYPsFfqOApDYolPr+AAAAAElFTkSuQmCC);
}
.gwd-coupon.gwd-jd[data-v-07d1070f]:hover:before,
.gwd-coupon.gwd-jd[data-v-07d1070f]:hover:after {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAsCAYAAABovuiKAAAAAXNSR0IArs4c6QAAAclJREFUSEvNlttNw0AQRe/MrgURChgThUeAmEcBKSEdQAl0AB2QDkIHlEIJlm3xTQl8BoyURePIkY3Fx6yDxPxFcq5m75x5EDYUJDqfWe666v2NkBmdgHs9r+RaGXEYwgwO1GKl0CJNZwDFDNyAKORoHyaKVGKlUBWLJJ+ywQtZCxuP/YXqFQyur/yFiiSZOGMTWIvAJ6NFlt0R0R45zAD4e1QHknrbsKOR6lnycaP8dnQC2gRHWoPraTcy4n4fwhAFQbenlW+1FtIqWrE1kIskiY2xzw6Yik/ilyYaZLskCQtj38GM4PJCo7OqWj0qFLTGN4S+8tfHpVvOfFgqhYosf3GgGHCx/DbHR+CdHf3T1mQzwwwG4N2+SqRFttaXX4G043M1P5VYe9RGEUy07/e0IsueZdQKjKXZw6Hap0b5P9L0gYjnncne2KiVfmNj37xbRAQAxMYGc+fcRMaJORyqDG+vbFlFvmOkIlvKTmEIYlZl83dk+/DzK9nluj4/g1ROE6sjQnY+XAjGHERx5yNivbL/zagt0vzeEZ5oawv27FRj0Wr4S/c7UAjgtlP3148IH6PbR8TlhRfVLSGVKT8+3uyd3SWT6r/fRdq0N2b1Td4AAAAASUVORK5CYII=);
}
.gwd-redpack[data-v-07d1070f] {
  padding-right: 5px;
  padding-left: 8px;
  background: white;
}
.gwd-redpack.gwd-taobao[data-v-07d1070f] {
  border: 1px solid #ff4400;
}
.gwd-redpack.gwd-taobao[data-v-07d1070f]:hover {
  background: #fff0e7;
}
.gwd-redpack.gwd-jd[data-v-07d1070f] {
  border: 1px solid #f9d2d3;
}
.gwd-redpack.gwd-jd[data-v-07d1070f]:hover {
  background: #fff0e7;
}
.gwd-tooltip[data-v-07d1070f] {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  z-index: 1;
  color: #3c3c3c;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: none;
  background: white;
  border-radius: 2px;
}
.gwd-tooltip span[data-v-07d1070f] {
  color: #3c3c3c;
}
.gwd-tooltip[data-v-07d1070f]:after {
  /* a triangle at bottom */
  content: " ";
  position: absolute;
  bottom: -10px;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent white transparent;
  transform: rotate(180deg);
}
</style></head><body class="zh_CN light" style="--app-height: 1278px;"><div id="root"><div class="ds-theme" style="--ds-rgb-hover: 0 0 0 / 4%; font-size: var(--ds-font-size-m); line-height: var(--ds-line-height-m);"><div class="cb86951c"><div class="cddfb2ed"></div><div class="c3ecdb44"><div class="dc04ec1d a02af2e6"><div class="a7f3a288 f0d4f23d"><div class="ds-icon" style="font-size: 44px; width: 44px; height: 44px; cursor: pointer;"><svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path id="path" d="M27.501 8.46875C27.249 8.3457 27.1406 8.58008 26.9932 8.69922C26.9434 8.73828 26.9004 8.78906 26.8584 8.83398C26.4902 9.22852 26.0605 9.48633 25.5 9.45508C24.6787 9.41016 23.9785 9.66797 23.3594 10.2969C23.2275 9.52148 22.79 9.05859 22.125 8.76172C21.7764 8.60742 21.4238 8.45312 21.1807 8.11719C21.0098 7.87891 20.9639 7.61328 20.8779 7.35156C20.8242 7.19336 20.7695 7.03125 20.5879 7.00391C20.3906 6.97266 20.3135 7.13867 20.2363 7.27734C19.9258 7.84375 19.8066 8.46875 19.8174 9.10156C19.8447 10.5234 20.4453 11.6562 21.6367 12.4629C21.7725 12.5547 21.8076 12.6484 21.7646 12.7832C21.6836 13.0605 21.5869 13.3301 21.501 13.6074C21.4473 13.7852 21.3662 13.8242 21.1768 13.7461C20.5225 13.4727 19.957 13.0684 19.458 12.5781C18.6104 11.7578 17.8438 10.8516 16.8877 10.1426C16.6631 9.97656 16.4395 9.82227 16.207 9.67578C15.2314 8.72656 16.335 7.94727 16.5898 7.85547C16.8574 7.75977 16.6826 7.42773 15.8193 7.43164C14.957 7.43555 14.167 7.72461 13.1611 8.10938C13.0137 8.16797 12.8594 8.21094 12.7002 8.24414C11.7871 8.07227 10.8389 8.0332 9.84766 8.14453C7.98242 8.35352 6.49219 9.23633 5.39648 10.7441C4.08105 12.5547 3.77148 14.6133 4.15039 16.7617C4.54883 19.0234 5.70215 20.8984 7.47559 22.3633C9.31348 23.8809 11.4307 24.625 13.8457 24.4824C15.3125 24.3984 16.9463 24.2012 18.7881 22.6406C19.2529 22.8711 19.7402 22.9629 20.5498 23.0332C21.1729 23.0918 21.7725 23.002 22.2373 22.9062C22.9648 22.752 22.9141 22.0781 22.6514 21.9531C20.5186 20.959 20.9863 21.3633 20.5605 21.0371C21.6445 19.752 23.2783 18.418 23.917 14.0977C23.9668 13.7539 23.9238 13.5391 23.917 13.2598C23.9131 13.0918 23.9512 13.0254 24.1445 13.0059C24.6787 12.9453 25.1973 12.7988 25.6738 12.5352C27.0557 11.7793 27.6123 10.5391 27.7441 9.05078C27.7637 8.82422 27.7402 8.58789 27.501 8.46875ZM15.46 21.8613C13.3926 20.2344 12.3906 19.6992 11.9766 19.7227C11.5898 19.7441 11.6592 20.1875 11.7441 20.4766C11.833 20.7617 11.9492 20.959 12.1123 21.209C12.2246 21.375 12.3018 21.623 12 21.8066C11.334 22.2207 10.1768 21.668 10.1221 21.6406C8.77539 20.8477 7.64941 19.7988 6.85547 18.3652C6.08984 16.9844 5.64453 15.5039 5.57129 13.9238C5.55176 13.541 5.66406 13.4062 6.04297 13.3379C6.54199 13.2461 7.05762 13.2266 7.55664 13.2988C9.66602 13.6074 11.4619 14.5527 12.9668 16.0469C13.8262 16.9004 14.4766 17.918 15.1465 18.9121C15.8584 19.9688 16.625 20.9746 17.6006 21.7988C17.9443 22.0879 18.2197 22.3086 18.4824 22.4707C17.6895 22.5586 16.3652 22.5781 15.46 21.8613ZM16.4502 15.4805C16.4502 15.3105 16.5859 15.1758 16.7568 15.1758C16.7949 15.1758 16.8301 15.1836 16.8613 15.1953C16.9033 15.2109 16.9424 15.2344 16.9727 15.2695C17.0273 15.3223 17.0586 15.4004 17.0586 15.4805C17.0586 15.6504 16.9229 15.7852 16.7529 15.7852C16.582 15.7852 16.4502 15.6504 16.4502 15.4805ZM19.5273 17.0625C19.3301 17.1426 19.1328 17.2129 18.9434 17.2207C18.6494 17.2344 18.3281 17.1152 18.1533 16.9688C17.8828 16.7422 17.6895 16.6152 17.6074 16.2168C17.5732 16.0469 17.5928 15.7852 17.623 15.6348C17.6934 15.3105 17.6152 15.1035 17.3877 14.9141C17.2012 14.7598 16.9658 14.7188 16.7061 14.7188C16.6094 14.7188 16.5205 14.6758 16.4541 14.6406C16.3457 14.5859 16.2568 14.4512 16.3418 14.2852C16.3691 14.2324 16.501 14.1016 16.5322 14.0781C16.8838 13.877 17.29 13.9434 17.666 14.0938C18.0146 14.2363 18.2773 14.498 18.6562 14.8672C19.0439 15.3145 19.1133 15.4395 19.334 15.7734C19.5078 16.0371 19.667 16.3066 19.7754 16.6152C19.8408 16.8066 19.7559 16.9648 19.5273 17.0625Z" fill-rule="nonzero" fill="#4D6BFE"></path></svg></div><div style="margin-top: 38px; display: flex;"><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-size: 28px;"><svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clippath id="clip1381_20236"><rect id="打开边栏0730" width="30.000000" height="30.000000" fill="white" fill-opacity="0"></rect></clippath></defs><rect id="打开边栏0730" width="30.000000" height="30.000000" fill="#FFFFFF" fill-opacity="0"></rect><g clip-path="url(#clip1381_20236)"><rect id="rect" x="11.572754" y="17.683594" rx="1.000947" width="5.995172" height="2.001895" transform="rotate(-42.841 11.572754 17.683594)" fill="currentColor" fill-opacity="1.000000"></rect><rect id="rect" x="16.033691" y="16.271484" rx="0.995190" width="6.002943" height="1.990380" transform="rotate(-139.147 16.033691 16.271484)" fill="currentColor" fill-opacity="1.000000"></rect><path id="path" d="M20.09 25.48L9.89 25.5C9.47 25.5 9.05 25.45 8.64 25.37C8.23 25.29 7.83 25.17 7.44 25C7.05 24.84 6.68 24.64 6.33 24.41C5.98 24.18 5.66 23.91 5.36 23.61C5.07 23.31 4.8 22.99 4.57 22.64C4.34 22.29 4.14 21.92 3.98 21.53C3.82 21.14 3.69 20.74 3.61 20.32C3.53 19.91 3.49 19.49 3.49 19.07L3.49 10.92C3.49 10.5 3.53 10.08 3.61 9.67C3.69 9.25 3.82 8.85 3.98 8.46C4.14 8.07 4.34 7.7 4.57 7.35C4.8 7 5.07 6.68 5.36 6.38C5.66 6.08 5.98 5.81 6.33 5.58C6.68 5.35 7.05 5.15 7.44 4.99C7.83 4.82 8.23 4.7 8.64 4.62C9.05 4.54 9.47 4.5 9.89 4.5L20.09 4.48C20.51 4.48 20.93 4.52 21.34 4.6C21.75 4.69 22.15 4.81 22.54 4.97C22.93 5.13 23.3 5.33 23.65 5.57C24 5.8 24.32 6.06 24.62 6.36C24.92 6.66 25.18 6.98 25.41 7.33C25.65 7.69 25.84 8.06 26.01 8.45C26.17 8.84 26.29 9.24 26.37 9.65C26.45 10.06 26.49 10.48 26.5 10.91L26.5 19.06C26.49 19.48 26.45 19.89 26.37 20.31C26.29 20.72 26.17 21.12 26.01 21.51C25.84 21.9 25.65 22.27 25.41 22.62C25.18 22.97 24.92 23.3 24.62 23.6C24.32 23.89 24 24.16 23.65 24.39C23.3 24.63 22.93 24.83 22.54 24.99C22.15 25.15 21.75 25.27 21.34 25.35C20.93 25.44 20.51 25.48 20.09 25.48ZM9.89 6.59C9.61 6.59 9.32 6.62 9.05 6.67C8.77 6.73 8.5 6.81 8.24 6.92C7.98 7.03 7.73 7.16 7.49 7.32C7.26 7.48 7.04 7.66 6.84 7.86C6.64 8.06 6.46 8.28 6.3 8.51C6.14 8.75 6.01 9 5.9 9.26C5.79 9.52 5.71 9.8 5.66 10.07C5.6 10.35 5.57 10.63 5.57 10.92L5.57 19.07C5.57 19.36 5.6 19.64 5.66 19.92C5.71 20.19 5.79 20.47 5.9 20.73C6.01 20.99 6.14 21.24 6.3 21.48C6.46 21.71 6.64 21.93 6.84 22.13C7.04 22.33 7.26 22.51 7.49 22.67C7.73 22.83 7.98 22.96 8.24 23.07C8.5 23.18 8.77 23.26 9.05 23.32C9.32 23.37 9.61 23.4 9.89 23.4L20.09 23.39C20.38 23.39 20.66 23.36 20.94 23.3C21.21 23.25 21.48 23.17 21.75 23.06C22.01 22.95 22.26 22.81 22.49 22.66C22.73 22.5 22.95 22.32 23.15 22.12C23.35 21.91 23.52 21.7 23.68 21.46C23.84 21.22 23.97 20.98 24.08 20.71C24.19 20.45 24.27 20.18 24.33 19.9C24.38 19.62 24.41 19.34 24.41 19.06L24.41 10.91C24.41 10.62 24.38 10.34 24.33 10.06C24.27 9.78 24.19 9.51 24.08 9.25C23.97 8.98 23.84 8.74 23.68 8.5C23.52 8.26 23.35 8.04 23.15 7.84C22.95 7.64 22.73 7.46 22.49 7.3C22.26 7.15 22.01 7.01 21.75 6.9C21.48 6.79 21.21 6.71 20.94 6.66C20.66 6.6 20.38 6.57 20.09 6.57L9.89 6.59Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="rect" d="M8.49 5.5L10.53 5.5L10.59 24.41L8.54 24.41L8.49 5.5Z" fill="currentColor" fill-opacity="1.000000" fill-rule="evenodd"></path></g></svg></div></div><div style="margin-top: 38px; display: flex;"><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-size: 28px;"><svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clippath id="clip1325_20400"><rect id="新建会话0730" width="30.000000" height="30.000000" fill="white" fill-opacity="0"></rect></clippath></defs><g clip-path="url(#clip1325_20400)"><path id="path" d="M10.51 26.52C10.35 26.52 10.19 26.49 10.04 26.43C9.89 26.37 9.76 26.28 9.64 26.17C9.53 26.06 9.44 25.93 9.37 25.78C9.31 25.63 9.28 25.48 9.28 25.32L9.25 22.87C8.89 22.82 8.53 22.74 8.19 22.62C7.84 22.5 7.51 22.36 7.19 22.19C6.86 22.01 6.56 21.81 6.28 21.58C5.99 21.36 5.73 21.11 5.49 20.83C5.25 20.56 5.04 20.27 4.85 19.96C4.67 19.65 4.51 19.33 4.38 18.99C4.25 18.65 4.16 18.3 4.09 17.95C4.03 17.6 4 17.24 4 16.88L4 10.38C4 9.99 4.03 9.6 4.11 9.21C4.19 8.82 4.31 8.45 4.46 8.08C4.61 7.72 4.8 7.37 5.03 7.04C5.25 6.72 5.5 6.41 5.78 6.13C6.07 5.86 6.38 5.61 6.71 5.39C7.04 5.17 7.4 4.98 7.77 4.83C8.14 4.68 8.52 4.57 8.91 4.49C9.31 4.41 9.7 4.38 10.11 4.38L14.35 4.38C14.5 4.38 14.63 4.4 14.77 4.46C14.9 4.51 15.02 4.59 15.12 4.69C15.22 4.79 15.3 4.9 15.35 5.03C15.41 5.16 15.43 5.3 15.43 5.44C15.43 5.58 15.41 5.71 15.35 5.84C15.3 5.97 15.22 6.09 15.12 6.19C15.02 6.29 14.9 6.37 14.77 6.42C14.63 6.47 14.5 6.5 14.35 6.5L10.11 6.5C9.85 6.5 9.59 6.53 9.34 6.58C9.08 6.62 8.83 6.7 8.6 6.8C8.36 6.89 8.13 7.01 7.91 7.15C7.7 7.3 7.5 7.46 7.31 7.64C7.13 7.82 6.97 8.01 6.82 8.22C6.68 8.44 6.56 8.66 6.46 8.9C6.36 9.13 6.28 9.37 6.23 9.62C6.18 9.87 6.16 10.12 6.16 10.38L6.16 16.88C6.16 17.14 6.18 17.39 6.23 17.65C6.29 17.9 6.36 18.15 6.46 18.39C6.56 18.62 6.69 18.85 6.83 19.07C6.98 19.28 7.15 19.48 7.33 19.66C7.52 19.85 7.72 20.01 7.94 20.15C8.16 20.3 8.39 20.42 8.63 20.52C8.87 20.62 9.13 20.69 9.38 20.74C9.64 20.79 9.9 20.82 10.17 20.82C10.33 20.82 10.49 20.85 10.64 20.91C10.79 20.97 10.92 21.06 11.04 21.17C11.15 21.28 11.24 21.41 11.31 21.56C11.37 21.71 11.4 21.86 11.41 22.02L11.42 23.53L14.15 21.56C14.85 21.07 15.62 20.82 16.48 20.82L19.87 20.82C20.13 20.82 20.38 20.79 20.64 20.74C20.89 20.69 21.14 20.62 21.38 20.52C21.62 20.42 21.85 20.3 22.06 20.16C22.28 20.02 22.48 19.86 22.66 19.68C22.84 19.5 23.01 19.3 23.15 19.09C23.29 18.88 23.42 18.66 23.52 18.42C23.61 18.19 23.69 17.94 23.74 17.69C23.79 17.44 23.82 17.19 23.82 16.94L23.82 13.58C23.82 13.44 23.84 13.3 23.9 13.17C23.95 13.04 24.03 12.93 24.13 12.83C24.23 12.73 24.35 12.65 24.48 12.6C24.62 12.54 24.75 12.52 24.9 12.52C25.04 12.52 25.18 12.54 25.31 12.6C25.44 12.65 25.56 12.73 25.66 12.83C25.76 12.93 25.84 13.04 25.9 13.17C25.95 13.3 25.98 13.44 25.98 13.58L25.98 16.94C25.98 17.33 25.94 17.72 25.86 18.11C25.78 18.5 25.67 18.87 25.51 19.24C25.36 19.6 25.17 19.95 24.95 20.27C24.73 20.6 24.47 20.9 24.19 21.18C23.9 21.46 23.6 21.71 23.26 21.93C22.93 22.15 22.58 22.33 22.21 22.48C21.83 22.63 21.45 22.75 21.06 22.83C20.67 22.9 20.27 22.94 19.87 22.94L17.19 22.94C16.33 22.94 15.56 23.19 14.86 23.69L11.24 26.29C11.02 26.44 10.78 26.52 10.51 26.52Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><rect id="rect" x="17.770508" y="6.396484" rx="1.062250" width="8.060087" height="2.124500" fill="currentColor" fill-opacity="1.000000"></rect><rect id="rect" x="20.718750" y="3.500000" rx="1.081197" width="2.162393" height="7.918844" fill="currentColor" fill-opacity="1.000000"></rect></g></svg></div></div><div style="flex: 1 1 0%;"></div><div class="_41b9122"><div class="ds-icon" style="font-size: 28px; width: 28px; height: 28px;"><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="300" height="300"><path d="M725.333 1024H298.667a128 128 0 0 1-128-128V128a128 128 0 0 1 128-128h426.666a128 128 0 0 1 128 128v768a128 128 0 0 1-128 128zM298.667 85.333A42.667 42.667 0 0 0 256 128v768a42.667 42.667 0 0 0 42.667 42.667h426.666A42.667 42.667 0 0 0 768 896V128a42.667 42.667 0 0 0-42.667-42.667z" fill="currentColor"></path><path d="M469.33299999999997 853.333a42.667 42.667 0 1 0 85.334 0 42.667 42.667 0 1 0-85.334 0zM554.667 213.333h-85.334a42.667 42.667 0 0 1 0-85.333h85.334a42.667 42.667 0 0 1 0 85.333z" fill="currentColor"></path></svg></div></div><div class="ede5bc47"><img class="fdf01f38" src="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/Kew1qbv9H88F_jlbJs70UjG3" alt="" aria-hidden="true"></div></div><div class="b8812f16 a2f3d50e _70b689f"><div class="_6969ec9"><div class="a5967822"></div><div class="e066abb8"><svg viewBox="-2 0 175 32" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs></defs><path id="path" d="M75.32 25.23L72.83 25.23L72.83 21.37L75.32 21.37C76.86 21.37 78.42 20.99 79.43 19.92C80.44 18.85 80.81 17.2 80.81 15.57C80.81 13.94 80.44 12.3 79.43 11.23C78.42 10.16 76.86 9.78 75.32 9.78C73.77 9.78 72.22 10.16 71.21 11.23C70.19 12.3 69.83 13.94 69.83 15.57L69.83 31.44L65.46 31.44L65.46 5.92L69.83 5.92L69.83 7.54L70.63 7.54C70.71 7.45 70.8 7.36 70.9 7.27C71.99 6.27 73.66 5.92 75.32 5.92C77.89 5.92 80.48 6.56 82.17 8.34C83.85 10.12 84.46 12.86 84.46 15.58C84.46 18.29 83.85 21.03 82.17 22.81C80.48 24.6 77.89 25.23 75.32 25.23Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M8.79 6.59L11.28 6.59L11.28 10.45L8.79 10.45C7.25 10.45 5.69 10.83 4.68 11.91C3.67 12.98 3.3 14.62 3.3 16.25C3.3 17.88 3.67 19.52 4.68 20.59C5.69 21.66 7.25 22.05 8.79 22.05C10.34 22.05 11.89 21.66 12.9 20.59C13.92 19.52 14.28 17.88 14.28 16.25L14.28 0.39L18.65 0.39L18.65 25.91L14.28 25.91L14.28 24.28L13.48 24.28C13.4 24.38 13.31 24.47 13.21 24.55C12.12 25.55 10.45 25.91 8.79 25.91C6.22 25.91 3.63 25.27 1.94 23.48C0.26 21.7 -0.35 18.97 -0.35 16.25C-0.35 13.53 0.26 10.8 1.94 9.01C3.63 7.23 6.22 6.59 8.79 6.59Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M40.59 15.91L40.59 17.46L28.95 17.46L28.95 14.36L36.67 14.36C36.49 13.23 36.08 12.18 35.36 11.42C34.3 10.31 32.69 9.91 31.08 9.91C29.48 9.91 27.86 10.31 26.81 11.42C25.76 12.52 25.38 14.22 25.38 15.91C25.38 17.6 25.76 19.3 26.81 20.41C27.86 21.52 29.48 21.91 31.08 21.91C32.69 21.91 34.3 21.52 35.36 20.41C35.5 20.25 35.64 20.08 35.76 19.9L40.08 19.9C39.71 21.24 39.1 22.45 38.2 23.4C36.44 25.25 33.75 25.91 31.08 25.91C28.41 25.91 25.72 25.25 23.97 23.4C22.21 21.55 21.58 18.72 21.58 15.91C21.58 13.1 22.21 10.27 23.97 8.42C25.72 6.58 28.41 5.92 31.08 5.92C33.75 5.92 36.44 6.58 38.2 8.42C39.96 10.27 40.59 13.1 40.59 15.91Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M62.52 15.91L62.52 17.46L50.88 17.46L50.88 14.36L58.61 14.36C58.43 13.23 58.02 12.18 57.29 11.42C56.24 10.31 54.63 9.91 53.02 9.91C51.42 9.91 49.8 10.31 48.75 11.42C47.7 12.52 47.32 14.22 47.32 15.91C47.32 17.6 47.7 19.3 48.75 20.41C49.8 21.52 51.42 21.91 53.02 21.91C54.63 21.91 56.24 21.52 57.29 20.41C57.44 20.25 57.58 20.08 57.7 19.9L62.02 19.9C61.64 21.24 61.04 22.45 60.14 23.4C58.38 25.25 55.69 25.91 53.02 25.91C50.35 25.91 47.66 25.25 45.9 23.4C44.15 21.55 43.52 18.72 43.52 15.91C43.52 13.1 44.15 10.27 45.9 8.42C47.66 6.58 50.35 5.92 53.02 5.92C55.69 5.92 58.38 6.58 60.14 8.42C61.89 10.27 62.52 13.1 62.52 15.91Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M96.9 25.91C99.57 25.91 102.26 25.52 104.01 24.43C105.77 23.34 106.4 21.67 106.4 20.01C106.4 18.35 105.77 16.68 104.01 15.59C102.26 14.5 99.57 14.11 96.9 14.11L96.99 14.11C95.85 14.11 94.7 13.96 93.96 13.53C93.21 13.11 92.94 12.46 92.94 11.82C92.94 11.17 93.21 10.53 93.96 10.1C94.7 9.68 95.85 9.53 96.99 9.53C98.13 9.53 99.28 9.68 100.03 10.1C100.78 10.53 101.04 11.17 101.04 11.82L105.49 11.82C105.49 10.16 104.92 8.49 103.34 7.4C101.75 6.31 99.32 5.92 96.9 5.92C94.48 5.92 92.05 6.31 90.46 7.4C88.87 8.49 88.3 10.16 88.3 11.82C88.3 13.48 88.87 15.15 90.46 16.24C92.05 17.33 94.48 17.72 96.9 17.72C98.16 17.72 99.53 17.87 100.36 18.29C101.19 18.71 101.48 19.36 101.48 20.01C101.48 20.65 101.19 21.3 100.36 21.72C99.53 22.14 98.26 22.3 97 22.3C95.74 22.3 94.47 22.14 93.65 21.72C92.82 21.3 92.52 20.65 92.52 20.01L87.4 20.01C87.4 21.67 88.03 23.34 89.78 24.43C91.54 25.52 94.22 25.91 96.9 25.91Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M128.33 15.91L128.33 17.46L116.69 17.46L116.69 14.36L124.42 14.36C124.24 13.23 123.83 12.18 123.1 11.42C122.05 10.31 120.44 9.91 118.83 9.91C117.23 9.91 115.61 10.31 114.56 11.42C113.51 12.52 113.13 14.22 113.13 15.91C113.13 17.6 113.51 19.3 114.56 20.41C115.61 21.52 117.23 21.91 118.83 21.91C120.44 21.91 122.05 21.52 123.1 20.41C123.25 20.25 123.39 20.08 123.51 19.9L127.83 19.9C127.45 21.24 126.85 22.45 125.95 23.4C124.19 25.25 121.5 25.91 118.83 25.91C116.16 25.91 113.47 25.25 111.71 23.4C109.96 21.55 109.33 18.72 109.33 15.91C109.33 13.1 109.96 10.27 111.71 8.42C113.47 6.58 116.16 5.92 118.83 5.92C121.5 5.92 124.19 6.58 125.95 8.42C127.7 10.27 128.33 13.1 128.33 15.91Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M150.27 15.91L150.27 17.46L138.63 17.46L138.63 14.36L146.36 14.36C146.17 13.23 145.77 12.18 145.04 11.42C143.99 10.31 142.37 9.91 140.77 9.91C139.17 9.91 137.55 10.31 136.5 11.42C135.44 12.52 135.07 14.22 135.07 15.91C135.07 17.6 135.44 19.3 136.5 20.41C137.55 21.52 139.17 21.91 140.77 21.91C142.37 21.91 143.99 21.52 145.04 20.41C145.19 20.25 145.32 20.08 145.45 19.9L149.77 19.9C149.39 21.24 148.79 22.45 147.88 23.4C146.13 25.25 143.44 25.91 140.77 25.91C138.1 25.91 135.4 25.25 133.65 23.4C131.9 21.55 131.27 18.72 131.27 15.91C131.27 13.1 131.9 10.27 133.65 8.42C135.4 6.58 138.1 5.92 140.77 5.92C143.44 5.92 146.13 6.58 147.88 8.42C149.64 10.27 150.27 13.1 150.27 15.91Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><rect id="rect" x="153.211426" y="-0.499512" width="4.371000" height="26.415646" fill="currentColor" fill-opacity="1.000000"></rect><path id="polygon" d="M165.04 15.31L172.21 25.91L166.79 25.91L159.62 15.31L166.79 6.81L172.21 6.81L165.04 15.31Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path></svg></div><div class="ds-icon _8a9564e" style="font-size: 24px; width: 24px; height: 24px;"><svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4.27777 24.2888L23.7222 24.2888C24.4279 24.2888 25 23.7167 25 23.0111C25 22.3054 24.4279 21.7333 23.7222 21.7333L4.27777 21.7333C3.57208 21.7333 3 22.3054 3 23.0111C3 23.7167 3.57208 24.2888 4.27777 24.2888Z" fill="currentColor"></path><path d="M13.2407 15.4001L23.7222 15.4001C24.4279 15.4001 25 14.8281 25 14.1224C25 13.4167 24.4279 12.8446 23.7222 12.8446L13.2407 12.8446C12.535 12.8446 11.963 13.4167 11.963 14.1224C11.963 14.8281 12.535 15.4001 13.2407 15.4001Z" fill="currentColor"></path><path d="M4.27777 6.55542L23.7222 6.55542C24.4279 6.55542 25 5.98334 25 5.27765C25 4.57197 24.4279 3.99989 23.7222 3.99989L4.27777 3.99989C3.57208 3.99989 3 4.57197 3 5.27765C3 5.98334 3.57208 6.55542 4.27777 6.55542Z" fill="currentColor"></path></svg></div></div><div class="ebaea5d2"><div class="_5a8ac7a"><div class="c7dddcde"><div class="_1c42ad7"><svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.10999 27C8.92999 27 8.76001 26.96 8.60001 26.9C8.43001 26.83 8.29 26.74 8.16 26.61C8.03 26.49 7.94 26.3499 7.87 26.1899C7.79999 26.0299 7.76001 25.8599 7.76001 25.6899L7.73001 23.04C7.34001 22.98 6.95001 22.8799 6.57001 22.7599C6.19001 22.6299 5.83001 22.48 5.48001 22.29C5.13001 22.1 4.79999 21.88 4.48999 21.63C4.17999 21.39 3.89 21.1199 3.63 20.82C3.37 20.52 3.13999 20.21 2.92999 19.87C2.72999 19.53 2.56001 19.18 2.42001 18.82C2.28001 18.45 2.17001 18.07 2.10001 17.69C2.03001 17.3 2 16.92 2 16.53V9.46995C2 9.03995 2.04 8.61995 2.12 8.19995C2.21 7.77995 2.34 7.36995 2.5 6.96995C2.67 6.57995 2.88 6.19995 3.12 5.84995C3.36 5.48995 3.64001 5.15995 3.95001 4.85995C4.26001 4.55995 4.59999 4.28995 4.95999 4.04995C5.32999 3.80995 5.70999 3.60995 6.10999 3.44995C6.51999 3.27995 6.94 3.15995 7.37 3.07995C7.79999 2.98995 8.23001 2.94995 8.67001 2.94995H13.3C13.46 2.94995 13.61 2.97995 13.76 3.03995C13.9 3.09995 14.03 3.17995 14.14 3.28995C14.25 3.39995 14.33 3.51995 14.39 3.65995C14.45 3.79995 14.48 3.94995 14.48 4.09995C14.48 4.25995 14.45 4.39995 14.39 4.54995C14.33 4.68995 14.25 4.80995 14.14 4.91995C14.03 5.02995 13.9 5.10995 13.76 5.16995C13.61 5.22995 13.46 5.25995 13.3 5.25995H8.67001C8.38001 5.25995 8.09999 5.27995 7.82999 5.33995C7.54999 5.38995 7.27999 5.46995 7.01999 5.57995C6.75999 5.67995 6.50999 5.80995 6.26999 5.96995C6.03999 6.11995 5.82 6.29995 5.62 6.48995C5.42 6.68995 5.23999 6.89995 5.07999 7.12995C4.92999 7.35995 4.78999 7.59995 4.67999 7.85995C4.57999 8.10995 4.49 8.37995 4.44 8.64995C4.38 8.91995 4.35999 9.18995 4.35999 9.46995V16.53C4.35999 16.81 4.38 17.08 4.44 17.36C4.5 17.63 4.58 17.9 4.69 18.16C4.8 18.42 4.93 18.67 5.09 18.9C5.25 19.13 5.43001 19.3499 5.64001 19.5499C5.84001 19.75 6.05999 19.92 6.29999 20.08C6.53999 20.24 6.79 20.37 7.06 20.47C7.32 20.58 7.6 20.66 7.88 20.72C8.16001 20.77 8.44001 20.7999 8.73001 20.7999C8.91001 20.7999 9.08 20.83 9.25 20.9C9.41 20.97 9.55999 21.0599 9.67999 21.18C9.80999 21.3099 9.91001 21.45 9.98001 21.61C10.05 21.77 10.08 21.94 10.09 22.11L10.1 23.74L13.08 21.61C13.84 21.07 14.69 20.7999 15.63 20.7999H19.32C19.61 20.7999 19.89 20.77 20.16 20.72C20.44 20.67 20.71 20.59 20.97 20.4799C21.23 20.3699 21.48 20.24 21.72 20.09C21.95 19.94 22.17 19.76 22.37 19.57C22.57 19.3699 22.75 19.16 22.91 18.93C23.07 18.7 23.2 18.46 23.31 18.2C23.41 17.95 23.5 17.68 23.55 17.41C23.61 17.14 23.63 16.87 23.63 16.59V12.94C23.63 12.79 23.66 12.64 23.72 12.5C23.78 12.36 23.87 12.23 23.98 12.13C24.09 12.02 24.22 11.93 24.36 11.88C24.51 11.82 24.66 11.79 24.82 11.79C24.97 11.79 25.12 11.82 25.27 11.88C25.41 11.93 25.54 12.02 25.65 12.13C25.76 12.23 25.85 12.36 25.91 12.5C25.97 12.64 26 12.79 26 12.94V16.59C26 17.02 25.95 17.44 25.87 17.86C25.78 18.28 25.66 18.69 25.49 19.08C25.32 19.48 25.11 19.8499 24.87 20.2099C24.63 20.57 24.35 20.9 24.04 21.2C23.73 21.5 23.39 21.7699 23.03 22.0099C22.67 22.2499 22.28 22.45 21.88 22.61C21.47 22.77 21.06 22.9 20.63 22.9799C20.2 23.07 19.76 23.11 19.32 23.11H16.4C15.47 23.11 14.62 23.3799 13.86 23.9199L9.91 26.74C9.67 26.91 9.39999 27 9.10999 27Z" fill="currentColor"></path><path d="M24.6805 5.14453H18.1874C17.5505 5.14453 17.0342 5.66086 17.0342 6.29778C17.0342 6.9347 17.5505 7.45102 18.1874 7.45102H24.6805C25.3175 7.45102 25.8338 6.9347 25.8338 6.29778C25.8338 5.66086 25.3175 5.14453 24.6805 5.14453Z" fill="currentColor"></path><path d="M22.6137 3.1804C22.6137 2.52848 22.0852 2 21.4333 2C20.7814 2 20.2529 2.52848 20.2529 3.1804V9.4168C20.2529 10.0687 20.7814 10.5972 21.4333 10.5972C22.0852 10.5972 22.6137 10.0687 22.6137 9.4168V3.1804Z" fill="currentColor"></path></svg></div>开启新对话</div></div></div><div class="_03210fb scrollable"><div class="_77cdc67 _8a693f3"><div class="_5d4b535"><div class="_48cdfc1">今天</div><div class="_83421f9" tabindex="0"><div class="c08e6e93">起名翻译及中文表达建议</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="b9cb4a74"></div></div><div class="_5d4b535"><div class="_48cdfc1">7 天内</div><div class="_83421f9 b64fb9ae" tabindex="0"><div class="c08e6e93">Java中Date与LocalDate区别及MySQL插入方式</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Exchange微服务接口路由设计建议</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Vite插件CORS问题解决方案</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Word转Markdown方法总结</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Win11升级Git版本方法指南</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">游戏道具流水表设计方案分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">贷款7万手续费3000年利率计算</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Vite项目启动日志问题分析与解决方案</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">4岁儿童发热病毒感染分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="b9cb4a74"></div></div><div class="_5d4b535"><div class="_48cdfc1">30 天内</div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Spring JDBC插入更新返回2原因</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">双足机器人训练框架实验总结</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">ICLR与IEEE学术会议区别解析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">JWT单点登录签名验证流程</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">研究生申请Listary Pro教育优惠</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">英语论文研究价值陈述时态使用指南</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Go语言与Java内存管理对比分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">计算机科学与工程学院英文翻译建议</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">JSON与JSONB的区别及使用场景</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">请详解这道题</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">BINARY(16)存储UUID的解释</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Tailwind CSS与React内联样式区别分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">国内npm安装加速方法总结</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Nakama与ioGame架构对比分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">SpringBoot构造函数@Autowired使用区别</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">计算效率高的翻译</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">使用MCP Server构建本地代码搜索系统</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">工程管理硕士论文选题研究</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">提升人形机器人研究应用性</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">软件生产率及其影响因素分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">卓越绩效模式领导三角与结果三角解析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">六西格玛中西格玛的统计学意义</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">编译时与运行时载入对比</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Navicat连接PostgreSQL列不存在问题</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">双足机器人研究CARS模型标注</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">分布式系统架构图实现与说明</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">gRPC技术原理及与WebSocket等协议对比</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Egg.js配置MySQL连接池数量方法</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">生成arXiv论文APA引用</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">通义千问模型推荐与使用指南</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">运行质量成本损失分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="b9cb4a74"></div></div><div class="_5d4b535"><div class="_48cdfc1">2025-06</div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Chrome插件命名建议及功能解析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">MySQL中LIMIT子句的使用方法</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">MySQL中LEFT JOIN与INNER JOIN的区别</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">质量成本分类与归类分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">npx与npm的区别及使用场景</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Graduate Thesis Introduction Writing Assistance</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Jenkins中npm安装yarn报错解决</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">ScaleCube-Cluster与Consul/ZooKeeper对比</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">新增activity_Id字段SQL语句</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">SQL查询语法错误分析与修正</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">游戏与应用订阅消费心理对比分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Unity 2022 LTS稳定开发推荐</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">TypeScript调度器开发模式禁用方案</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">飞书服务单元测试实现与启动命令</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Egg.js后端发送飞书消息教程</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">索尼XM4耳机均衡器调校建议</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="b9cb4a74"></div></div><div class="_5d4b535"><div class="_48cdfc1">2025-05</div><div class="_83421f9" tabindex="0"><div class="c08e6e93">CARS模型详解与论文引言撰写</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">阿里云sdk 如何修改一个实例的镜像</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">AI金融项目进度计划制定优化建议</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">AI金融软件项目进度管理策略</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">工程发包与招标问题解答</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">软件成本测试主要影响因素分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">QFD质量屋的定义与应用</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">编程中state与status的选择区别</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">MySQL表新增is_read字段SQL</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">决策树法分析单阶段决策方案</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Rust适合微服务开发分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">新车轮胎更换周期及注意事项</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Java遍历ArrayBlockingQueue方法总结</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Java SDK构造iOS推送通知示例</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Driver Attention Shifted by Non-Driving Devices</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">ISO9000标准中质量的定义解析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">VC与天使轮融资解析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">杨铭宇黄焖鸡事件质量问题分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">小米推送SDK错误27001原因及解决方案</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">阿里云释放实例错误分析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Node.js Native Build Tools Missing Error</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">MySQL查询配对服务器SQL语句</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">使用AON表示法构建项目网络图</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">COVID-19疫情下项目管理特殊挑战</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">多音调服务器数据流分析与流程图</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">SQL查询选修全部课程的学生</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="b9cb4a74"></div></div><div class="_5d4b535"><div class="_48cdfc1">2025-04</div><div class="_83421f9" tabindex="0"><div class="c08e6e93">1万元年利率1.7%一年收益计算</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Navicat中数据表截断操作详解</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">王阳明诗心学解析与现代启示</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">工程信息系统系统分析六步骤</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">雨课堂数据组织与实体完整性解析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">SQL查询详解：选修所有课程的学生</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">大数据技术原理与应用概述</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Spring Boot定时器cron与fixedRate区别</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">计算2028年5月剩余时间</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">MySQL优化：笛卡尔积转自然连接原因</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">MySQL LEFT JOIN条件放置位置选择</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">王阳明知行合一思想解析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">MySQL索引优化设计建议</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">MEM研究生涨薪方案设计</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">企业战略规划方法CSF、SST、BSP解析</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Spring Boot中Dao注解区别与替换</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="_83421f9" tabindex="0"><div class="c08e6e93">Linux查找定时HTTP请求程序方法</div><div class="_2090548" tabindex="0"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0" clip-rule="evenodd"></path></svg></div></div><div class="_2f87737"></div><div class="eaaaba55"></div></div><div class="b9cb4a74"></div></div></div><div class="de3d058c"></div></div><div class="c7f51894"><div class="a1e75851"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px; margin-right: 8px;"><svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="300" height="300"><path d="M725.333 1024H298.667a128 128 0 0 1-128-128V128a128 128 0 0 1 128-128h426.666a128 128 0 0 1 128 128v768a128 128 0 0 1-128 128zM298.667 85.333A42.667 42.667 0 0 0 256 128v768a42.667 42.667 0 0 0 42.667 42.667h426.666A42.667 42.667 0 0 0 768 896V128a42.667 42.667 0 0 0-42.667-42.667z" fill="currentColor"></path><path d="M469.33299999999997 853.333a42.667 42.667 0 1 0 85.334 0 42.667 42.667 0 1 0-85.334 0zM554.667 213.333h-85.334a42.667 42.667 0 0 1 0-85.333h85.334a42.667 42.667 0 0 1 0 85.333z" fill="currentColor"></path></svg></div>下载 App<span class="_106b9f9"><svg width="36" height="16" viewBox="0 0 36 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="36" height="16" rx="8" fill="url(#paint0_linear_5707_2640)" fill-opacity=".85"></rect><path d="M7.113 4.86h1.1l3.65 5.31h.04V4.86h1.09V12h-1.06l-3.69-5.37h-.04V12h-1.09V4.86zm7.285 0h5.13v.93h-4.04v2.08h3.8v.93h-3.8v2.27h4.21V12h-5.3V4.86zm5.756 0h1.23l1.39 5.56h.04l1.46-5.56h1.14l1.46 5.56h.04l1.39-5.56h1.23L27.475 12h-1.16l-1.45-5.51h-.04L23.375 12h-1.17l-2.05-7.14z" fill="#fff"></path><defs><lineargradient id="paint0_linear_5707_2640" y1="8" x2="36" y2="8" gradientUnits="userSpaceOnUse"><stop stop-color="#4D6BFE"></stop><stop offset="1" stop-color="#6948D6"></stop></lineargradient></defs></svg></span></div><div class="c6ab9234"><div class="ede5bc47"><img class="fdf01f38" src="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/Kew1qbv9H88F_jlbJs70UjG3" alt="" aria-hidden="true"></div><div class="_7d65532">个人信息</div></div></div></div></div><div class="_4cbcd96 _7d10bb1"></div><div class="_7780f2e"><div class="_765a5cd"><div class="_2be88ba"><div class="f8d1e4c0"><div style="flex: 1 1 0%; min-width: 0px; display: flex; place-content: center; z-index: 12;"><div class="d8ed659a" tabindex="0" style="outline: none;">Java中Date与LocalDate区别及MySQL插入方式</div></div><div class="_62b4800"></div></div><div class="_0efe408"><div class="ds-icon d7829b2f _2e7d873" style="font-size: 24px; width: 24px; height: 24px; color: rgb(139, 139, 139);"><svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.7222 4H4.27776C3.57207 4 3 4.57207 3 5.27776C3 5.98345 3.57207 6.55553 4.27776 6.55553H23.7222C24.4279 6.55553 25 5.98345 25 5.27776C25 4.57207 24.4279 4 23.7222 4Z" fill="currentColor"></path><path d="M14.7593 12.8887H4.27776C3.57207 12.8887 3 13.4607 3 14.1664C3 14.8721 3.57207 15.4442 4.27776 15.4442H14.7593C15.465 15.4442 16.037 14.8721 16.037 14.1664C16.037 13.4607 15.465 12.8887 14.7593 12.8887Z" fill="currentColor"></path><path d="M23.7222 21.7334H4.27776C3.57207 21.7334 3 22.3055 3 23.0112C3 23.7169 3.57207 24.2889 4.27776 24.2889H23.7222C24.4279 24.2889 25 23.7169 25 23.0112C25 22.3055 24.4279 21.7334 23.7222 21.7334Z" fill="currentColor"></path></svg></div><div class="ds-icon d7829b2f _23ecf90" style="font-size: 24px; width: 24px; height: 24px; color: rgb(139, 139, 139);"><svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.10999 27C8.92999 27 8.76001 26.96 8.60001 26.9C8.43001 26.83 8.29 26.74 8.16 26.61C8.03 26.49 7.94 26.3499 7.87 26.1899C7.79999 26.0299 7.76001 25.8599 7.76001 25.6899L7.73001 23.04C7.34001 22.98 6.95001 22.8799 6.57001 22.7599C6.19001 22.6299 5.83001 22.48 5.48001 22.29C5.13001 22.1 4.79999 21.88 4.48999 21.63C4.17999 21.39 3.89 21.1199 3.63 20.82C3.37 20.52 3.13999 20.21 2.92999 19.87C2.72999 19.53 2.56001 19.18 2.42001 18.82C2.28001 18.45 2.17001 18.07 2.10001 17.69C2.03001 17.3 2 16.92 2 16.53V9.46995C2 9.03995 2.04 8.61995 2.12 8.19995C2.21 7.77995 2.34 7.36995 2.5 6.96995C2.67 6.57995 2.88 6.19995 3.12 5.84995C3.36 5.48995 3.64001 5.15995 3.95001 4.85995C4.26001 4.55995 4.59999 4.28995 4.95999 4.04995C5.32999 3.80995 5.70999 3.60995 6.10999 3.44995C6.51999 3.27995 6.94 3.15995 7.37 3.07995C7.79999 2.98995 8.23001 2.94995 8.67001 2.94995H13.3C13.46 2.94995 13.61 2.97995 13.76 3.03995C13.9 3.09995 14.03 3.17995 14.14 3.28995C14.25 3.39995 14.33 3.51995 14.39 3.65995C14.45 3.79995 14.48 3.94995 14.48 4.09995C14.48 4.25995 14.45 4.39995 14.39 4.54995C14.33 4.68995 14.25 4.80995 14.14 4.91995C14.03 5.02995 13.9 5.10995 13.76 5.16995C13.61 5.22995 13.46 5.25995 13.3 5.25995H8.67001C8.38001 5.25995 8.09999 5.27995 7.82999 5.33995C7.54999 5.38995 7.27999 5.46995 7.01999 5.57995C6.75999 5.67995 6.50999 5.80995 6.26999 5.96995C6.03999 6.11995 5.82 6.29995 5.62 6.48995C5.42 6.68995 5.23999 6.89995 5.07999 7.12995C4.92999 7.35995 4.78999 7.59995 4.67999 7.85995C4.57999 8.10995 4.49 8.37995 4.44 8.64995C4.38 8.91995 4.35999 9.18995 4.35999 9.46995V16.53C4.35999 16.81 4.38 17.08 4.44 17.36C4.5 17.63 4.58 17.9 4.69 18.16C4.8 18.42 4.93 18.67 5.09 18.9C5.25 19.13 5.43001 19.3499 5.64001 19.5499C5.84001 19.75 6.05999 19.92 6.29999 20.08C6.53999 20.24 6.79 20.37 7.06 20.47C7.32 20.58 7.6 20.66 7.88 20.72C8.16001 20.77 8.44001 20.7999 8.73001 20.7999C8.91001 20.7999 9.08 20.83 9.25 20.9C9.41 20.97 9.55999 21.0599 9.67999 21.18C9.80999 21.3099 9.91001 21.45 9.98001 21.61C10.05 21.77 10.08 21.94 10.09 22.11L10.1 23.74L13.08 21.61C13.84 21.07 14.69 20.7999 15.63 20.7999H19.32C19.61 20.7999 19.89 20.77 20.16 20.72C20.44 20.67 20.71 20.59 20.97 20.4799C21.23 20.3699 21.48 20.24 21.72 20.09C21.95 19.94 22.17 19.76 22.37 19.57C22.57 19.3699 22.75 19.16 22.91 18.93C23.07 18.7 23.2 18.46 23.31 18.2C23.41 17.95 23.5 17.68 23.55 17.41C23.61 17.14 23.63 16.87 23.63 16.59V12.94C23.63 12.79 23.66 12.64 23.72 12.5C23.78 12.36 23.87 12.23 23.98 12.13C24.09 12.02 24.22 11.93 24.36 11.88C24.51 11.82 24.66 11.79 24.82 11.79C24.97 11.79 25.12 11.82 25.27 11.88C25.41 11.93 25.54 12.02 25.65 12.13C25.76 12.23 25.85 12.36 25.91 12.5C25.97 12.64 26 12.79 26 12.94V16.59C26 17.02 25.95 17.44 25.87 17.86C25.78 18.28 25.66 18.69 25.49 19.08C25.32 19.48 25.11 19.8499 24.87 20.2099C24.63 20.57 24.35 20.9 24.04 21.2C23.73 21.5 23.39 21.7699 23.03 22.0099C22.67 22.2499 22.28 22.45 21.88 22.61C21.47 22.77 21.06 22.9 20.63 22.9799C20.2 23.07 19.76 23.11 19.32 23.11H16.4C15.47 23.11 14.62 23.3799 13.86 23.9199L9.91 26.74C9.67 26.91 9.39999 27 9.10999 27Z" fill="currentColor"></path><path d="M24.6805 5.14453H18.1874C17.5505 5.14453 17.0342 5.66086 17.0342 6.29778C17.0342 6.9347 17.5505 7.45102 18.1874 7.45102H24.6805C25.3175 7.45102 25.8338 6.9347 25.8338 6.29778C25.8338 5.66086 25.3175 5.14453 24.6805 5.14453Z" fill="currentColor"></path><path d="M22.6137 3.1804C22.6137 2.52848 22.0852 2 21.4333 2C20.7814 2 20.2529 2.52848 20.2529 3.1804V9.4168C20.2529 10.0687 20.7814 10.5972 21.4333 10.5972C22.0852 10.5972 22.6137 10.0687 22.6137 9.4168V3.1804Z" fill="currentColor"></path></svg></div></div></div><div class="_3919b83"><div class="_8f60047 scrollable"><div class="_0f72b0b"><div class="dad65929"><div class="_9663006"><div class="fbb737a4">在java中Date与LocalDate的区别是什么，插入mysql时一样吗</div><div class="e13328ad _3a26104"><div class="_425ea0b"><div class="ds-flex _78e0558" style="align-items: flex-end; gap: 0px;"><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.65169 12.9243C3.68173 13.1045 3.74181 13.2748 3.80189 13.445C3.87198 13.6052 3.96211 13.7654 4.06225 13.9156C4.16238 14.0658 4.27253 14.206 4.4027 14.3362C4.52286 14.4663 4.66306 14.5765 4.81326 14.6766C4.96346 14.7768 5.11366 14.8569 5.28389 14.927C5.44411 14.9971 5.61434 15.0571 5.79459 15.0872C5.97483 15.1272 6.14506 15.1373 6.3253 15.1373V16.9196C6.30739 16.9196 6.28949 16.9195 6.27159 16.9193C5.9991 16.9158 5.72659 16.8859 5.4541 16.8295C5.16371 16.7694 4.88334 16.6893 4.61298 16.5692C4.3326 16.459 4.08226 16.3188 3.83193 16.1586C3.59161 15.9884 3.3613 15.7981 3.15102 15.5878C2.94074 15.3776 2.7605 15.1473 2.59027 14.9069C2.43006 14.6566 2.28986 14.3962 2.17972 14.1259C2.06957 13.8455 1.97944 13.5651 1.91936 13.2747C1.86929 12.9843 1.83926 12.684 1.83926 12.3936V6.26532C1.83926 5.96492 1.86929 5.67456 1.91936 5.38417C1.97944 5.09378 2.06957 4.80338 2.17972 4.53302C2.28986 4.26265 2.43006 4.0023 2.59027 3.75197C2.7605 3.50163 2.94074 3.27132 3.15102 3.06104C3.3613 2.85076 3.59161 2.67052 3.83193 2.50029C4.08226 2.33006 4.3326 2.19987 4.61298 2.07971C4.88334 1.96956 5.16371 1.87943 5.4541 1.81935C5.74449 1.75927 6.03491 1.73926 6.3253 1.73926H12.3934C12.6838 1.73926 12.9842 1.75927 13.2746 1.81935C13.555 1.87943 13.8354 1.96956 14.1158 2.07971C14.3861 2.19987 14.6465 2.33006 14.8868 2.50029C15.1371 2.67052 15.3574 2.85076 15.5677 3.06104C15.778 3.27132 15.9582 3.50163 16.1284 3.75197C16.2887 4.0023 16.4288 4.26265 16.539 4.53302C16.6592 4.80338 16.7393 5.09378 16.7994 5.38417C16.8558 5.65722 16.8858 5.93024 16.8892 6.21161C16.8894 6.22948 16.8895 6.24739 16.8895 6.26532H15.1271C15.1271 6.08508 15.1071 5.90486 15.067 5.72462C15.037 5.55439 14.9869 5.38415 14.9168 5.21392C14.8467 5.04369 14.7566 4.88347 14.6665 4.73327C14.5664 4.58307 14.4462 4.45289 14.326 4.32271C14.1959 4.19254 14.0557 4.08239 13.9055 3.98226C13.7553 3.88212 13.6051 3.79202 13.4348 3.72193C13.2746 3.65184 13.1044 3.60174 12.9242 3.5717C12.7539 3.53165 12.5737 3.51163 12.3934 3.51163H6.3253C6.14506 3.51163 5.97483 3.53165 5.79459 3.5717C5.61434 3.60174 5.44411 3.65184 5.28389 3.72193C5.11366 3.79202 4.96346 3.88212 4.81326 3.98226C4.66306 4.08239 4.52286 4.19254 4.4027 4.32271C4.27253 4.45289 4.16238 4.58307 4.06225 4.73327C3.96211 4.88347 3.87198 5.04369 3.80189 5.21392C3.74181 5.38415 3.68173 5.55439 3.65169 5.72462C3.61164 5.90486 3.60164 6.08508 3.60164 6.26532V12.3936C3.60164 12.5638 3.61164 12.744 3.65169 12.9243Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.66972 21.6772C9.39936 21.567 9.13902 21.4268 8.8987 21.2566C8.64836 21.0964 8.42804 20.9061 8.21776 20.6959C8.00748 20.4856 7.81723 20.2553 7.65701 20.015C7.4968 19.7646 7.3566 19.5043 7.24646 19.2239C7.12629 18.9535 7.04621 18.6731 6.98613 18.3727C6.92605 18.0823 6.89601 17.792 6.89601 17.4915V11.3733C6.89601 11.0729 6.92605 10.7825 6.98613 10.4922C7.04621 10.1918 7.12629 9.91137 7.24646 9.64101C7.3566 9.36063 7.4968 9.10028 7.65701 8.85996C7.81723 8.60962 8.00748 8.37931 8.21776 8.16903C8.42804 7.95875 8.64836 7.76849 8.8987 7.60828C9.13902 7.43805 9.39936 7.29785 9.66972 7.1877C9.94009 7.07755 10.2205 6.98745 10.5108 6.92737C10.8012 6.86729 11.0916 6.83725 11.392 6.83725H17.4602C17.7506 6.83725 18.041 6.86729 18.3313 6.92737C18.6217 6.98745 18.9021 7.07755 19.1725 7.1877C19.4529 7.29785 19.7032 7.43805 19.9535 7.60828C20.1938 7.76849 20.4242 7.95875 20.6345 8.16903C20.8447 8.37931 21.025 8.60962 21.1952 8.85996C21.3554 9.10028 21.4956 9.36063 21.6058 9.64101C21.7159 9.91137 21.806 10.1918 21.8661 10.4922C21.9162 10.7825 21.9462 11.0729 21.9462 11.3733V17.4915C21.9462 17.792 21.9162 18.0823 21.8661 18.3727C21.806 18.6731 21.7159 18.9535 21.6058 19.2239C21.4956 19.5043 21.3554 19.7646 21.1952 20.015C21.025 20.2553 20.8447 20.4856 20.6345 20.6959C20.4242 20.9061 20.1938 21.0964 19.9535 21.2566C19.7032 21.4268 19.4529 21.567 19.1725 21.6772C18.9021 21.7973 18.6217 21.8774 18.3313 21.9375C18.041 21.9976 17.7506 22.0276 17.4602 22.0276H11.392C11.0916 22.0276 10.8012 21.9976 10.5108 21.9375C10.2205 21.8774 9.94009 21.7973 9.66972 21.6772ZM10.8613 8.6697C11.0316 8.63966 11.2118 8.61965 11.392 8.61965H17.4602C17.6404 8.61965 17.8107 8.63966 17.9909 8.6697C18.1611 8.70975 18.3314 8.75983 18.5016 8.82992C18.6618 8.90001 18.822 8.98012 18.9722 9.08026C19.1224 9.18039 19.2626 9.30055 19.3828 9.42071C19.513 9.55088 19.6231 9.69109 19.7232 9.84129C19.8234 9.99149 19.9035 10.1517 19.9736 10.3219C20.0437 10.4821 20.0937 10.6624 20.1338 10.8326C20.1638 11.0129 20.1838 11.1931 20.1838 11.3733V17.4915C20.1838 17.6718 20.1638 17.852 20.1338 18.0323C20.0937 18.2125 20.0437 18.3828 19.9736 18.543C19.9035 18.7132 19.8234 18.8734 19.7232 19.0236C19.6231 19.1738 19.513 19.314 19.3828 19.4342C19.2626 19.5643 19.1224 19.6845 18.9722 19.7846C18.822 19.8848 18.6618 19.9649 18.5016 20.035C18.3314 20.1051 18.1611 20.1551 17.9909 20.1952C17.8107 20.2252 17.6404 20.2452 17.4602 20.2452H11.392C11.2118 20.2452 11.0316 20.2252 10.8613 20.1952C10.6811 20.1551 10.5108 20.1051 10.3506 20.035C10.1804 19.9649 10.0202 19.8848 9.87 19.7846C9.72982 19.6845 9.58962 19.5643 9.45945 19.4342C9.33929 19.314 9.21913 19.1738 9.119 19.0236C9.01886 18.8734 8.93875 18.7132 8.86866 18.543C8.79857 18.3828 8.74847 18.2125 8.71843 18.0323C8.67838 17.852 8.65836 17.6718 8.65836 17.4915V11.3733C8.65836 11.1931 8.67838 11.0129 8.71843 10.8326C8.74847 10.6624 8.79857 10.4821 8.86866 10.3219C8.93875 10.1517 9.01886 9.99149 9.119 9.84129C9.21913 9.69109 9.33929 9.55088 9.45945 9.42071C9.58962 9.30055 9.72982 9.18039 9.87 9.08026C10.0202 8.98012 10.1804 8.90001 10.3506 8.82992C10.5108 8.75983 10.6811 8.70975 10.8613 8.6697Z" fill="currentColor"></path></svg></div></div><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px; margin-left: 14px; opacity: 1;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.712 2.79a1.854 1.854 0 0 1 2.623 0l1.673 1.672a1.854 1.854 0 0 1 0 2.623l-9.714 9.714a2.91 2.91 0 0 1-2.058.853H1.945a.8.8 0 0 1-.8-.8v-2.29c0-.773.307-1.513.853-2.058l9.714-9.715zm1.492 1.13c-.1-.1-.261-.1-.361 0l-1.327 1.326 2.035 2.035 1.327-1.326c.1-.1.1-.262 0-.362L13.204 3.92zm-.783 4.491l-2.035-2.034-7.258 7.257a1.31 1.31 0 0 0-.384.927v1.492h1.492c.348 0 .681-.138.927-.384l7.258-7.258z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M17.772 17.608c.35 0 .633-.283.633-.633v-.492a.633.633 0 0 0-.633-.633H9.858L8.1 17.608h9.672z" fill="currentColor"></path></svg></div></div></div></div></div></div><div class="_4f9bf79 _43c05b5"><div class="_7eb2358 _58dfa60"><svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path id="path" d="M27.501 8.46875C27.249 8.3457 27.1406 8.58008 26.9932 8.69922C26.9434 8.73828 26.9004 8.78906 26.8584 8.83398C26.4902 9.22852 26.0605 9.48633 25.5 9.45508C24.6787 9.41016 23.9785 9.66797 23.3594 10.2969C23.2275 9.52148 22.79 9.05859 22.125 8.76172C21.7764 8.60742 21.4238 8.45312 21.1807 8.11719C21.0098 7.87891 20.9639 7.61328 20.8779 7.35156C20.8242 7.19336 20.7695 7.03125 20.5879 7.00391C20.3906 6.97266 20.3135 7.13867 20.2363 7.27734C19.9258 7.84375 19.8066 8.46875 19.8174 9.10156C19.8447 10.5234 20.4453 11.6562 21.6367 12.4629C21.7725 12.5547 21.8076 12.6484 21.7646 12.7832C21.6836 13.0605 21.5869 13.3301 21.501 13.6074C21.4473 13.7852 21.3662 13.8242 21.1768 13.7461C20.5225 13.4727 19.957 13.0684 19.458 12.5781C18.6104 11.7578 17.8438 10.8516 16.8877 10.1426C16.6631 9.97656 16.4395 9.82227 16.207 9.67578C15.2314 8.72656 16.335 7.94727 16.5898 7.85547C16.8574 7.75977 16.6826 7.42773 15.8193 7.43164C14.957 7.43555 14.167 7.72461 13.1611 8.10938C13.0137 8.16797 12.8594 8.21094 12.7002 8.24414C11.7871 8.07227 10.8389 8.0332 9.84766 8.14453C7.98242 8.35352 6.49219 9.23633 5.39648 10.7441C4.08105 12.5547 3.77148 14.6133 4.15039 16.7617C4.54883 19.0234 5.70215 20.8984 7.47559 22.3633C9.31348 23.8809 11.4307 24.625 13.8457 24.4824C15.3125 24.3984 16.9463 24.2012 18.7881 22.6406C19.2529 22.8711 19.7402 22.9629 20.5498 23.0332C21.1729 23.0918 21.7725 23.002 22.2373 22.9062C22.9648 22.752 22.9141 22.0781 22.6514 21.9531C20.5186 20.959 20.9863 21.3633 20.5605 21.0371C21.6445 19.752 23.2783 18.418 23.917 14.0977C23.9668 13.7539 23.9238 13.5391 23.917 13.2598C23.9131 13.0918 23.9512 13.0254 24.1445 13.0059C24.6787 12.9453 25.1973 12.7988 25.6738 12.5352C27.0557 11.7793 27.6123 10.5391 27.7441 9.05078C27.7637 8.82422 27.7402 8.58789 27.501 8.46875ZM15.46 21.8613C13.3926 20.2344 12.3906 19.6992 11.9766 19.7227C11.5898 19.7441 11.6592 20.1875 11.7441 20.4766C11.833 20.7617 11.9492 20.959 12.1123 21.209C12.2246 21.375 12.3018 21.623 12 21.8066C11.334 22.2207 10.1768 21.668 10.1221 21.6406C8.77539 20.8477 7.64941 19.7988 6.85547 18.3652C6.08984 16.9844 5.64453 15.5039 5.57129 13.9238C5.55176 13.541 5.66406 13.4062 6.04297 13.3379C6.54199 13.2461 7.05762 13.2266 7.55664 13.2988C9.66602 13.6074 11.4619 14.5527 12.9668 16.0469C13.8262 16.9004 14.4766 17.918 15.1465 18.9121C15.8584 19.9688 16.625 20.9746 17.6006 21.7988C17.9443 22.0879 18.2197 22.3086 18.4824 22.4707C17.6895 22.5586 16.3652 22.5781 15.46 21.8613ZM16.4502 15.4805C16.4502 15.3105 16.5859 15.1758 16.7568 15.1758C16.7949 15.1758 16.8301 15.1836 16.8613 15.1953C16.9033 15.2109 16.9424 15.2344 16.9727 15.2695C17.0273 15.3223 17.0586 15.4004 17.0586 15.4805C17.0586 15.6504 16.9229 15.7852 16.7529 15.7852C16.582 15.7852 16.4502 15.6504 16.4502 15.4805ZM19.5273 17.0625C19.3301 17.1426 19.1328 17.2129 18.9434 17.2207C18.6494 17.2344 18.3281 17.1152 18.1533 16.9688C17.8828 16.7422 17.6895 16.6152 17.6074 16.2168C17.5732 16.0469 17.5928 15.7852 17.623 15.6348C17.6934 15.3105 17.6152 15.1035 17.3877 14.9141C17.2012 14.7598 16.9658 14.7188 16.7061 14.7188C16.6094 14.7188 16.5205 14.6758 16.4541 14.6406C16.3457 14.5859 16.2568 14.4512 16.3418 14.2852C16.3691 14.2324 16.501 14.1016 16.5322 14.0781C16.8838 13.877 17.29 13.9434 17.666 14.0938C18.0146 14.2363 18.2773 14.498 18.6562 14.8672C19.0439 15.3145 19.1133 15.4395 19.334 15.7734C19.5078 16.0371 19.667 16.3066 19.7754 16.6152C19.8408 16.8066 19.7559 16.9648 19.5273 17.0625Z" fill-rule="nonzero" fill="#4D6BFE"></path></svg></div><div class="ds-markdown ds-markdown--block" style="--ds-md-zoom: 1.143;"><h1>Date与LocalDate的区别及MySQL插入方式</h1><h2>主要区别</h2><ol start="1"><li><p class="ds-markdown-paragraph"><strong>所属包和版本</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>java.util.Date</code>：来自Java最初的版本，存在于java.util包</p></li><li><p class="ds-markdown-paragraph"><code>LocalDate</code>：来自Java 8的新日期时间API，存在于java.time包</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>设计理念</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>：表示特定的瞬间，精确到毫秒，包含日期和时间信息</p></li><li><p class="ds-markdown-paragraph"><code>LocalDate</code>：只表示日期部分(年月日)，不包含时间和时区信息</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>可变性</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>是可变的(可以通过set方法修改)</p></li><li><p class="ds-markdown-paragraph"><code>LocalDate</code>是不可变的(任何修改都会返回新实例)</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>线程安全</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>不是线程安全的</p></li><li><p class="ds-markdown-paragraph"><code>LocalDate</code>是线程安全的</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>API设计</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>的API设计较差(很多方法已废弃)</p></li><li><p class="ds-markdown-paragraph"><code>LocalDate</code>提供了丰富且直观的日期操作方法</p></li></ul></li></ol><h2>插入MySQL时的区别</h2><ol start="1"><li><p class="ds-markdown-paragraph"><strong>对应MySQL类型</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>通常对应MySQL的<code>DATETIME</code>或<code>TIMESTAMP</code></p></li><li><p class="ds-markdown-paragraph"><code>LocalDate</code>对应MySQL的<code>DATE</code>类型</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>JDBC处理</strong>：</p><ul><li><p class="ds-markdown-paragraph">对于<code>Date</code>：可以直接使用<code>PreparedStatement.setDate()</code></p></li><li><p class="ds-markdown-paragraph">对于<code>LocalDate</code>：需要使用<code>PreparedStatement.setObject()</code><br>或<code>PreparedStatement.setDate(java.sql.Date.valueOf(localDate))</code></p></li></ul></li></ol><h2>示例代码</h2><div class="md-code-block md-code-block-light"><div class="md-code-block-banner-wrap"><div class="md-code-block-banner md-code-block-banner-lite"><div class="_121d384"><div class="d2a24f03"><span class="d813de27">java</span></div><div class="d2a24f03"><div class="efa13877"><div role="button" class="ds-button ds-button--secondary ds-button--borderless ds-button--rect ds-button--m _7db3914" tabindex="0" style="margin-right: 8px; font-size: 13px; height: 28px; padding: 0px 4px; --button-text-color: var(--dsr-text-2);"><div class="ds-button__icon"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.65169 12.9243C3.68173 13.1045 3.74181 13.2748 3.80189 13.445C3.87198 13.6052 3.96211 13.7654 4.06225 13.9156C4.16238 14.0658 4.27253 14.206 4.4027 14.3362C4.52286 14.4663 4.66306 14.5765 4.81326 14.6766C4.96346 14.7768 5.11366 14.8569 5.28389 14.927C5.44411 14.9971 5.61434 15.0571 5.79459 15.0872C5.97483 15.1272 6.14506 15.1373 6.3253 15.1373V16.9196C6.30739 16.9196 6.28949 16.9195 6.27159 16.9193C5.9991 16.9158 5.72659 16.8859 5.4541 16.8295C5.16371 16.7694 4.88334 16.6893 4.61298 16.5692C4.3326 16.459 4.08226 16.3188 3.83193 16.1586C3.59161 15.9884 3.3613 15.7981 3.15102 15.5878C2.94074 15.3776 2.7605 15.1473 2.59027 14.9069C2.43006 14.6566 2.28986 14.3962 2.17972 14.1259C2.06957 13.8455 1.97944 13.5651 1.91936 13.2747C1.86929 12.9843 1.83926 12.684 1.83926 12.3936V6.26532C1.83926 5.96492 1.86929 5.67456 1.91936 5.38417C1.97944 5.09378 2.06957 4.80338 2.17972 4.53302C2.28986 4.26265 2.43006 4.0023 2.59027 3.75197C2.7605 3.50163 2.94074 3.27132 3.15102 3.06104C3.3613 2.85076 3.59161 2.67052 3.83193 2.50029C4.08226 2.33006 4.3326 2.19987 4.61298 2.07971C4.88334 1.96956 5.16371 1.87943 5.4541 1.81935C5.74449 1.75927 6.03491 1.73926 6.3253 1.73926H12.3934C12.6838 1.73926 12.9842 1.75927 13.2746 1.81935C13.555 1.87943 13.8354 1.96956 14.1158 2.07971C14.3861 2.19987 14.6465 2.33006 14.8868 2.50029C15.1371 2.67052 15.3574 2.85076 15.5677 3.06104C15.778 3.27132 15.9582 3.50163 16.1284 3.75197C16.2887 4.0023 16.4288 4.26265 16.539 4.53302C16.6592 4.80338 16.7393 5.09378 16.7994 5.38417C16.8558 5.65722 16.8858 5.93024 16.8892 6.21161C16.8894 6.22948 16.8895 6.24739 16.8895 6.26532H15.1271C15.1271 6.08508 15.1071 5.90486 15.067 5.72462C15.037 5.55439 14.9869 5.38415 14.9168 5.21392C14.8467 5.04369 14.7566 4.88347 14.6665 4.73327C14.5664 4.58307 14.4462 4.45289 14.326 4.32271C14.1959 4.19254 14.0557 4.08239 13.9055 3.98226C13.7553 3.88212 13.6051 3.79202 13.4348 3.72193C13.2746 3.65184 13.1044 3.60174 12.9242 3.5717C12.7539 3.53165 12.5737 3.51163 12.3934 3.51163H6.3253C6.14506 3.51163 5.97483 3.53165 5.79459 3.5717C5.61434 3.60174 5.44411 3.65184 5.28389 3.72193C5.11366 3.79202 4.96346 3.88212 4.81326 3.98226C4.66306 4.08239 4.52286 4.19254 4.4027 4.32271C4.27253 4.45289 4.16238 4.58307 4.06225 4.73327C3.96211 4.88347 3.87198 5.04369 3.80189 5.21392C3.74181 5.38415 3.68173 5.55439 3.65169 5.72462C3.61164 5.90486 3.60164 6.08508 3.60164 6.26532V12.3936C3.60164 12.5638 3.61164 12.744 3.65169 12.9243Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.66972 21.6772C9.39936 21.567 9.13902 21.4268 8.8987 21.2566C8.64836 21.0964 8.42804 20.9061 8.21776 20.6959C8.00748 20.4856 7.81723 20.2553 7.65701 20.015C7.4968 19.7646 7.3566 19.5043 7.24646 19.2239C7.12629 18.9535 7.04621 18.6731 6.98613 18.3727C6.92605 18.0823 6.89601 17.792 6.89601 17.4915V11.3733C6.89601 11.0729 6.92605 10.7825 6.98613 10.4922C7.04621 10.1918 7.12629 9.91137 7.24646 9.64101C7.3566 9.36063 7.4968 9.10028 7.65701 8.85996C7.81723 8.60962 8.00748 8.37931 8.21776 8.16903C8.42804 7.95875 8.64836 7.76849 8.8987 7.60828C9.13902 7.43805 9.39936 7.29785 9.66972 7.1877C9.94009 7.07755 10.2205 6.98745 10.5108 6.92737C10.8012 6.86729 11.0916 6.83725 11.392 6.83725H17.4602C17.7506 6.83725 18.041 6.86729 18.3313 6.92737C18.6217 6.98745 18.9021 7.07755 19.1725 7.1877C19.4529 7.29785 19.7032 7.43805 19.9535 7.60828C20.1938 7.76849 20.4242 7.95875 20.6345 8.16903C20.8447 8.37931 21.025 8.60962 21.1952 8.85996C21.3554 9.10028 21.4956 9.36063 21.6058 9.64101C21.7159 9.91137 21.806 10.1918 21.8661 10.4922C21.9162 10.7825 21.9462 11.0729 21.9462 11.3733V17.4915C21.9462 17.792 21.9162 18.0823 21.8661 18.3727C21.806 18.6731 21.7159 18.9535 21.6058 19.2239C21.4956 19.5043 21.3554 19.7646 21.1952 20.015C21.025 20.2553 20.8447 20.4856 20.6345 20.6959C20.4242 20.9061 20.1938 21.0964 19.9535 21.2566C19.7032 21.4268 19.4529 21.567 19.1725 21.6772C18.9021 21.7973 18.6217 21.8774 18.3313 21.9375C18.041 21.9976 17.7506 22.0276 17.4602 22.0276H11.392C11.0916 22.0276 10.8012 21.9976 10.5108 21.9375C10.2205 21.8774 9.94009 21.7973 9.66972 21.6772ZM10.8613 8.6697C11.0316 8.63966 11.2118 8.61965 11.392 8.61965H17.4602C17.6404 8.61965 17.8107 8.63966 17.9909 8.6697C18.1611 8.70975 18.3314 8.75983 18.5016 8.82992C18.6618 8.90001 18.822 8.98012 18.9722 9.08026C19.1224 9.18039 19.2626 9.30055 19.3828 9.42071C19.513 9.55088 19.6231 9.69109 19.7232 9.84129C19.8234 9.99149 19.9035 10.1517 19.9736 10.3219C20.0437 10.4821 20.0937 10.6624 20.1338 10.8326C20.1638 11.0129 20.1838 11.1931 20.1838 11.3733V17.4915C20.1838 17.6718 20.1638 17.852 20.1338 18.0323C20.0937 18.2125 20.0437 18.3828 19.9736 18.543C19.9035 18.7132 19.8234 18.8734 19.7232 19.0236C19.6231 19.1738 19.513 19.314 19.3828 19.4342C19.2626 19.5643 19.1224 19.6845 18.9722 19.7846C18.822 19.8848 18.6618 19.9649 18.5016 20.035C18.3314 20.1051 18.1611 20.1551 17.9909 20.1952C17.8107 20.2252 17.6404 20.2452 17.4602 20.2452H11.392C11.2118 20.2452 11.0316 20.2252 10.8613 20.1952C10.6811 20.1551 10.5108 20.1051 10.3506 20.035C10.1804 19.9649 10.0202 19.8848 9.87 19.7846C9.72982 19.6845 9.58962 19.5643 9.45945 19.4342C9.33929 19.314 9.21913 19.1738 9.119 19.0236C9.01886 18.8734 8.93875 18.7132 8.86866 18.543C8.79857 18.3828 8.74847 18.2125 8.71843 18.0323C8.67838 17.852 8.65836 17.6718 8.65836 17.4915V11.3733C8.65836 11.1931 8.67838 11.0129 8.71843 10.8326C8.74847 10.6624 8.79857 10.4821 8.86866 10.3219C8.93875 10.1517 9.01886 9.99149 9.119 9.84129C9.21913 9.69109 9.33929 9.55088 9.45945 9.42071C9.58962 9.30055 9.72982 9.18039 9.87 9.08026C10.0202 8.98012 10.1804 8.90001 10.3506 8.82992C10.5108 8.75983 10.6811 8.70975 10.8613 8.6697Z" fill="currentColor"></path></svg></div></div><span class="code-info-button-text">复制</span></div><div role="button" class="ds-button ds-button--secondary ds-button--borderless ds-button--rect ds-button--s _7db3914" tabindex="0" style="padding: 0px 4px; height: 28px; --button-text-color: var(--dsr-text-2); --button-font-size: 13px;"><div class="ds-button__icon"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 2.55a.97.97 0 0 1 .982.956v13.04a.97.97 0 0 1-.982.957.97.97 0 0 1-.982-.956V3.507A.97.97 0 0 1 12 2.55z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M19.418 9.808c.382.375.37.971-.027 1.332l-6.7 6.085a1.04 1.04 0 0 1-1.41-.025.905.905 0 0 1 .027-1.332l6.7-6.085a1.04 1.04 0 0 1 1.41.025z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M4.582 9.808a1.04 1.04 0 0 1 1.41-.025l6.7 6.085c.397.361.409.957.027 1.332a1.04 1.04 0 0 1-1.41.025l-6.7-6.085a.905.905 0 0 1-.027-1.332z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M3.068 16.46a.97.97 0 0 1 .983.956v1.739c0 .432.36.782.803.782h14.291c.445 0 .804-.35.804-.782v-1.739a.97.97 0 0 1 .983-.956.97.97 0 0 1 .982.956v1.739c0 1.488-1.24 2.695-2.769 2.695H4.855c-1.53 0-2.77-1.207-2.77-2.695v-1.739a.97.97 0 0 1 .983-.956z" fill="currentColor"></path></svg></div></div><span class="code-info-button-text">下载</span></div></div></div></div></div></div><pre><span class="token comment">// 使用Date插入MySQL</span>
<span class="token class-name">Date</span> utilDate <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Date</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token class-name">PreparedStatement</span> stmt1 <span class="token operator">=</span> connection<span class="token punctuation">.</span><span class="token function">prepareStatement</span><span class="token punctuation">(</span><span class="token string">"INSERT INTO table(datetime_col) VALUES (?)"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
stmt1<span class="token punctuation">.</span><span class="token function">setDate</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token keyword">new</span> <span class="token class-name"><span class="token namespace">java<span class="token punctuation">.</span>sql<span class="token punctuation">.</span></span>Date</span><span class="token punctuation">(</span>utilDate<span class="token punctuation">.</span><span class="token function">getTime</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 使用LocalDate插入MySQL</span>
<span class="token class-name">LocalDate</span> localDate <span class="token operator">=</span> <span class="token class-name">LocalDate</span><span class="token punctuation">.</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token class-name">PreparedStatement</span> stmt2 <span class="token operator">=</span> connection<span class="token punctuation">.</span><span class="token function">prepareStatement</span><span class="token punctuation">(</span><span class="token string">"INSERT INTO table(date_col) VALUES (?)"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
stmt2<span class="token punctuation">.</span><span class="token function">setObject</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> localDate<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// 或者</span>
stmt2<span class="token punctuation">.</span><span class="token function">setDate</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token class-name"><span class="token namespace">java<span class="token punctuation">.</span>sql<span class="token punctuation">.</span></span>Date</span><span class="token punctuation">.</span><span class="token function">valueOf</span><span class="token punctuation">(</span>localDate<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span></pre></div><h2>建议</h2><ol start="1"><li><p class="ds-markdown-paragraph">新项目建议使用<code>LocalDate</code>，它是现代API且更符合业务需求(大多数业务只需要日期部分)</p></li><li><p class="ds-markdown-paragraph">如果需要时间部分，可以使用<code>LocalDateTime</code></p></li><li><p class="ds-markdown-paragraph">如果需要处理时区，可以使用<code>ZonedDateTime</code></p></li></ol><p class="ds-markdown-paragraph">MySQL中应根据实际需求选择合适的数据类型：<code>DATE</code>、<code>DATETIME</code>或<code>TIMESTAMP</code>。</p></div><div class="ds-flex" style="margin-top: 12px; height: 20px; margin-bottom: 20px; align-items: center; gap: 10px;"><div class="ds-flex _965abe9" style="align-items: center; gap: 16px;"><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.65169 12.9243C3.68173 13.1045 3.74181 13.2748 3.80189 13.445C3.87198 13.6052 3.96211 13.7654 4.06225 13.9156C4.16238 14.0658 4.27253 14.206 4.4027 14.3362C4.52286 14.4663 4.66306 14.5765 4.81326 14.6766C4.96346 14.7768 5.11366 14.8569 5.28389 14.927C5.44411 14.9971 5.61434 15.0571 5.79459 15.0872C5.97483 15.1272 6.14506 15.1373 6.3253 15.1373V16.9196C6.30739 16.9196 6.28949 16.9195 6.27159 16.9193C5.9991 16.9158 5.72659 16.8859 5.4541 16.8295C5.16371 16.7694 4.88334 16.6893 4.61298 16.5692C4.3326 16.459 4.08226 16.3188 3.83193 16.1586C3.59161 15.9884 3.3613 15.7981 3.15102 15.5878C2.94074 15.3776 2.7605 15.1473 2.59027 14.9069C2.43006 14.6566 2.28986 14.3962 2.17972 14.1259C2.06957 13.8455 1.97944 13.5651 1.91936 13.2747C1.86929 12.9843 1.83926 12.684 1.83926 12.3936V6.26532C1.83926 5.96492 1.86929 5.67456 1.91936 5.38417C1.97944 5.09378 2.06957 4.80338 2.17972 4.53302C2.28986 4.26265 2.43006 4.0023 2.59027 3.75197C2.7605 3.50163 2.94074 3.27132 3.15102 3.06104C3.3613 2.85076 3.59161 2.67052 3.83193 2.50029C4.08226 2.33006 4.3326 2.19987 4.61298 2.07971C4.88334 1.96956 5.16371 1.87943 5.4541 1.81935C5.74449 1.75927 6.03491 1.73926 6.3253 1.73926H12.3934C12.6838 1.73926 12.9842 1.75927 13.2746 1.81935C13.555 1.87943 13.8354 1.96956 14.1158 2.07971C14.3861 2.19987 14.6465 2.33006 14.8868 2.50029C15.1371 2.67052 15.3574 2.85076 15.5677 3.06104C15.778 3.27132 15.9582 3.50163 16.1284 3.75197C16.2887 4.0023 16.4288 4.26265 16.539 4.53302C16.6592 4.80338 16.7393 5.09378 16.7994 5.38417C16.8558 5.65722 16.8858 5.93024 16.8892 6.21161C16.8894 6.22948 16.8895 6.24739 16.8895 6.26532H15.1271C15.1271 6.08508 15.1071 5.90486 15.067 5.72462C15.037 5.55439 14.9869 5.38415 14.9168 5.21392C14.8467 5.04369 14.7566 4.88347 14.6665 4.73327C14.5664 4.58307 14.4462 4.45289 14.326 4.32271C14.1959 4.19254 14.0557 4.08239 13.9055 3.98226C13.7553 3.88212 13.6051 3.79202 13.4348 3.72193C13.2746 3.65184 13.1044 3.60174 12.9242 3.5717C12.7539 3.53165 12.5737 3.51163 12.3934 3.51163H6.3253C6.14506 3.51163 5.97483 3.53165 5.79459 3.5717C5.61434 3.60174 5.44411 3.65184 5.28389 3.72193C5.11366 3.79202 4.96346 3.88212 4.81326 3.98226C4.66306 4.08239 4.52286 4.19254 4.4027 4.32271C4.27253 4.45289 4.16238 4.58307 4.06225 4.73327C3.96211 4.88347 3.87198 5.04369 3.80189 5.21392C3.74181 5.38415 3.68173 5.55439 3.65169 5.72462C3.61164 5.90486 3.60164 6.08508 3.60164 6.26532V12.3936C3.60164 12.5638 3.61164 12.744 3.65169 12.9243Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.66972 21.6772C9.39936 21.567 9.13902 21.4268 8.8987 21.2566C8.64836 21.0964 8.42804 20.9061 8.21776 20.6959C8.00748 20.4856 7.81723 20.2553 7.65701 20.015C7.4968 19.7646 7.3566 19.5043 7.24646 19.2239C7.12629 18.9535 7.04621 18.6731 6.98613 18.3727C6.92605 18.0823 6.89601 17.792 6.89601 17.4915V11.3733C6.89601 11.0729 6.92605 10.7825 6.98613 10.4922C7.04621 10.1918 7.12629 9.91137 7.24646 9.64101C7.3566 9.36063 7.4968 9.10028 7.65701 8.85996C7.81723 8.60962 8.00748 8.37931 8.21776 8.16903C8.42804 7.95875 8.64836 7.76849 8.8987 7.60828C9.13902 7.43805 9.39936 7.29785 9.66972 7.1877C9.94009 7.07755 10.2205 6.98745 10.5108 6.92737C10.8012 6.86729 11.0916 6.83725 11.392 6.83725H17.4602C17.7506 6.83725 18.041 6.86729 18.3313 6.92737C18.6217 6.98745 18.9021 7.07755 19.1725 7.1877C19.4529 7.29785 19.7032 7.43805 19.9535 7.60828C20.1938 7.76849 20.4242 7.95875 20.6345 8.16903C20.8447 8.37931 21.025 8.60962 21.1952 8.85996C21.3554 9.10028 21.4956 9.36063 21.6058 9.64101C21.7159 9.91137 21.806 10.1918 21.8661 10.4922C21.9162 10.7825 21.9462 11.0729 21.9462 11.3733V17.4915C21.9462 17.792 21.9162 18.0823 21.8661 18.3727C21.806 18.6731 21.7159 18.9535 21.6058 19.2239C21.4956 19.5043 21.3554 19.7646 21.1952 20.015C21.025 20.2553 20.8447 20.4856 20.6345 20.6959C20.4242 20.9061 20.1938 21.0964 19.9535 21.2566C19.7032 21.4268 19.4529 21.567 19.1725 21.6772C18.9021 21.7973 18.6217 21.8774 18.3313 21.9375C18.041 21.9976 17.7506 22.0276 17.4602 22.0276H11.392C11.0916 22.0276 10.8012 21.9976 10.5108 21.9375C10.2205 21.8774 9.94009 21.7973 9.66972 21.6772ZM10.8613 8.6697C11.0316 8.63966 11.2118 8.61965 11.392 8.61965H17.4602C17.6404 8.61965 17.8107 8.63966 17.9909 8.6697C18.1611 8.70975 18.3314 8.75983 18.5016 8.82992C18.6618 8.90001 18.822 8.98012 18.9722 9.08026C19.1224 9.18039 19.2626 9.30055 19.3828 9.42071C19.513 9.55088 19.6231 9.69109 19.7232 9.84129C19.8234 9.99149 19.9035 10.1517 19.9736 10.3219C20.0437 10.4821 20.0937 10.6624 20.1338 10.8326C20.1638 11.0129 20.1838 11.1931 20.1838 11.3733V17.4915C20.1838 17.6718 20.1638 17.852 20.1338 18.0323C20.0937 18.2125 20.0437 18.3828 19.9736 18.543C19.9035 18.7132 19.8234 18.8734 19.7232 19.0236C19.6231 19.1738 19.513 19.314 19.3828 19.4342C19.2626 19.5643 19.1224 19.6845 18.9722 19.7846C18.822 19.8848 18.6618 19.9649 18.5016 20.035C18.3314 20.1051 18.1611 20.1551 17.9909 20.1952C17.8107 20.2252 17.6404 20.2452 17.4602 20.2452H11.392C11.2118 20.2452 11.0316 20.2252 10.8613 20.1952C10.6811 20.1551 10.5108 20.1051 10.3506 20.035C10.1804 19.9649 10.0202 19.8848 9.87 19.7846C9.72982 19.6845 9.58962 19.5643 9.45945 19.4342C9.33929 19.314 9.21913 19.1738 9.119 19.0236C9.01886 18.8734 8.93875 18.7132 8.86866 18.543C8.79857 18.3828 8.74847 18.2125 8.71843 18.0323C8.67838 17.852 8.65836 17.6718 8.65836 17.4915V11.3733C8.65836 11.1931 8.67838 11.0129 8.71843 10.8326C8.74847 10.6624 8.79857 10.4821 8.86866 10.3219C8.93875 10.1517 9.01886 9.99149 9.119 9.84129C9.21913 9.69109 9.33929 9.55088 9.45945 9.42071C9.58962 9.30055 9.72982 9.18039 9.87 9.08026C10.0202 8.98012 10.1804 8.90001 10.3506 8.82992C10.5108 8.75983 10.6811 8.70975 10.8613 8.6697Z" fill="currentColor"></path></svg></div></div><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px; opacity: 1;"><svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clippath id="clip1258_20811"><rect id="重新生成" width="20.000000" height="20.000000" fill="white" fill-opacity="0"></rect></clippath></defs><rect id="重新生成" width="20.000000" height="20.000000" fill="#FFFFFF" fill-opacity="0"></rect><g clip-path="url(#clip1258_20811)"><path id="path" d="M17.01 7.63L13.98 7.62C13.88 7.62 13.79 7.6 13.7 7.56C13.62 7.52 13.54 7.47 13.47 7.4C13.4 7.33 13.35 7.25 13.32 7.16C13.28 7.07 13.26 6.98 13.26 6.88C13.26 6.79 13.28 6.69 13.32 6.6C13.35 6.51 13.4 6.43 13.47 6.36C13.54 6.3 13.62 6.24 13.7 6.21C13.79 6.17 13.88 6.15 13.98 6.15L15.57 6.16C15.67 6.16 15.76 6.14 15.85 6.1C15.94 6.06 16.01 6.01 16.08 5.94C16.15 5.87 16.2 5.79 16.23 5.7C16.27 5.61 16.29 5.52 16.29 5.42L16.3 3.89C16.3 3.79 16.32 3.7 16.36 3.61C16.39 3.52 16.44 3.44 16.51 3.37C16.58 3.3 16.66 3.25 16.74 3.21C16.83 3.17 16.92 3.16 17.02 3.16C17.11 3.16 17.2 3.17 17.29 3.21C17.38 3.25 17.46 3.3 17.52 3.37C17.59 3.44 17.64 3.52 17.68 3.61C17.71 3.7 17.73 3.79 17.73 3.89L17.72 6.9C17.72 7 17.71 7.09 17.67 7.18C17.63 7.27 17.58 7.34 17.52 7.41C17.45 7.48 17.37 7.53 17.29 7.57C17.2 7.61 17.11 7.63 17.01 7.63Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M2.31 16.29L2.32 13.3C2.32 13.21 2.34 13.11 2.37 13.02C2.41 12.93 2.46 12.85 2.53 12.78C2.6 12.71 2.67 12.66 2.76 12.62C2.85 12.58 2.94 12.56 3.03 12.56L6.07 12.57C6.16 12.57 6.25 12.59 6.34 12.63C6.43 12.67 6.51 12.72 6.57 12.79C6.64 12.86 6.69 12.94 6.73 13.03C6.76 13.12 6.78 13.22 6.78 13.32C6.78 13.41 6.76 13.51 6.73 13.6C6.69 13.69 6.64 13.77 6.57 13.84C6.51 13.91 6.43 13.96 6.34 14C6.25 14.04 6.16 14.06 6.07 14.06L4.47 14.05C4.38 14.05 4.29 14.07 4.2 14.11C4.11 14.15 4.03 14.2 3.97 14.27C3.9 14.34 3.85 14.42 3.81 14.51C3.78 14.6 3.76 14.7 3.76 14.8L3.75 16.29C3.75 16.39 3.73 16.48 3.69 16.58C3.65 16.67 3.6 16.75 3.54 16.82C3.47 16.89 3.39 16.94 3.3 16.98C3.22 17.01 3.13 17.03 3.03 17.03C2.94 17.03 2.85 17.02 2.76 16.98C2.67 16.94 2.59 16.89 2.52 16.82C2.46 16.75 2.4 16.67 2.37 16.58C2.33 16.49 2.31 16.39 2.31 16.29Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M9.88 18.01C9.51 18.01 9.15 17.99 8.79 17.94C8.42 17.89 8.07 17.82 7.71 17.73C7.36 17.63 7.02 17.51 6.68 17.37C6.34 17.23 6.02 17.07 5.7 16.89C5.39 16.7 5.09 16.5 4.8 16.28C4.52 16.05 4.25 15.81 3.99 15.55C3.74 15.29 3.5 15.02 3.29 14.73C3.07 14.44 2.88 14.13 2.7 13.82L4.15 13.05C4.32 13.35 4.51 13.64 4.72 13.91C4.93 14.18 5.17 14.43 5.42 14.66C5.67 14.9 5.94 15.11 6.23 15.3C6.52 15.49 6.83 15.66 7.14 15.81C7.46 15.95 7.78 16.07 8.12 16.16C8.45 16.25 8.8 16.32 9.14 16.36C9.49 16.39 9.83 16.4 10.18 16.39C10.53 16.37 10.87 16.33 11.22 16.26C11.56 16.19 11.89 16.09 12.21 15.97C12.54 15.84 12.85 15.7 13.15 15.53C13.45 15.35 13.74 15.16 14.01 14.94C14.28 14.72 14.53 14.49 14.76 14.23C14.99 13.97 15.2 13.7 15.38 13.41C15.57 13.12 15.73 12.82 15.87 12.5C16 12.19 16.11 11.87 16.2 11.53C16.28 11.2 16.34 10.87 16.36 10.52C16.37 10.42 16.4 10.33 16.44 10.24C16.48 10.15 16.54 10.07 16.61 10C16.69 9.93 16.77 9.87 16.86 9.84C16.96 9.8 17.05 9.77 17.16 9.77C17.27 9.77 17.38 9.79 17.49 9.83C17.6 9.87 17.7 9.94 17.78 10.02C17.86 10.1 17.92 10.2 17.96 10.3C18 10.41 18.01 10.52 18 10.64C17.98 10.89 17.95 11.13 17.91 11.38C17.86 11.62 17.81 11.87 17.74 12.11C17.68 12.35 17.6 12.58 17.51 12.82C17.42 13.05 17.32 13.28 17.21 13.5C17.1 13.73 16.98 13.95 16.85 14.16C16.71 14.37 16.57 14.58 16.42 14.78C16.27 14.98 16.11 15.17 15.94 15.36C15.77 15.54 15.59 15.72 15.41 15.89C15.22 16.06 15.03 16.22 14.83 16.37C14.63 16.52 14.42 16.66 14.2 16.79C13.99 16.93 13.77 17.05 13.54 17.16C13.31 17.27 13.08 17.37 12.85 17.46C12.61 17.55 12.37 17.63 12.13 17.7C11.88 17.77 11.64 17.83 11.39 17.87C11.14 17.92 10.89 17.96 10.63 17.98C10.38 18 10.13 18.01 9.88 18.01Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M2.85 10.27C2.73 10.28 2.62 10.26 2.51 10.22C2.4 10.17 2.31 10.11 2.23 10.03C2.14 9.95 2.08 9.85 2.04 9.74C2 9.63 1.99 9.52 2 9.41C2.03 8.98 2.1 8.56 2.2 8.15C2.3 7.73 2.43 7.33 2.6 6.94C2.76 6.54 2.96 6.16 3.19 5.8C3.41 5.44 3.67 5.1 3.95 4.77C4.24 4.45 4.54 4.15 4.88 3.88C5.21 3.6 5.56 3.35 5.93 3.13C6.3 2.91 6.69 2.73 7.09 2.57C7.5 2.41 7.91 2.28 8.33 2.19C8.75 2.09 9.18 2.03 9.62 2.01C10.05 1.98 10.48 1.99 10.91 2.03C11.35 2.07 11.77 2.14 12.19 2.25C12.61 2.36 13.02 2.5 13.42 2.67C13.81 2.84 14.19 3.04 14.56 3.28C14.92 3.51 15.27 3.77 15.59 4.05C15.91 4.34 16.21 4.64 16.48 4.98C16.75 5.31 17 5.66 17.21 6.03L15.78 6.83C15.61 6.54 15.42 6.25 15.2 5.99C14.98 5.73 14.74 5.48 14.49 5.25C14.23 5.02 13.96 4.82 13.66 4.63C13.37 4.45 13.07 4.29 12.75 4.15C12.44 4.01 12.11 3.9 11.77 3.82C11.44 3.73 11.1 3.67 10.76 3.64C10.41 3.61 10.07 3.6 9.72 3.62C9.37 3.64 9.03 3.69 8.69 3.77C8.36 3.84 8.03 3.94 7.71 4.07C7.38 4.2 7.08 4.35 6.78 4.52C6.48 4.7 6.2 4.89 5.94 5.11C5.67 5.33 5.43 5.57 5.2 5.83C4.97 6.08 4.77 6.36 4.59 6.65C4.41 6.94 4.25 7.24 4.12 7.55C3.98 7.87 3.88 8.19 3.8 8.52C3.72 8.85 3.66 9.19 3.64 9.53C3.63 9.62 3.6 9.72 3.56 9.81C3.52 9.9 3.46 9.98 3.39 10.05C3.32 10.12 3.23 10.17 3.14 10.21C3.05 10.25 2.95 10.27 2.85 10.27Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path></g></svg></div></div><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M18.14 9.428l-.003.019L17.114 15a3.774 3.774 0 0 1-1.313 2.27 3.673 3.673 0 0 1-2.438.894H3.895c-.57 0-1.055-.205-1.458-.614a2.033 2.033 0 0 1-.604-1.48v-5.438c0-.578.201-1.071.604-1.48.402-.41.888-.614 1.458-.614h1.932l2.317-5.293c.24-.548.6-.948 1.083-1.2.599-.312 1.156-.278 1.672.103.782.577 1.172 1.355 1.172 2.334V6.997h3.994c.638-.007 1.167.235 1.588.726.421.492.584 1.06.488 1.705zm-2.076-2.6c.686-.007 1.262.256 1.714.785.453.529.63 1.146.527 1.84v.004l-.005.02v.003l-1.022 5.552a3.943 3.943 0 0 1-1.37 2.368 3.838 3.838 0 0 1-2.545.933H3.895a2.137 2.137 0 0 1-1.576-.663 2.203 2.203 0 0 1-.652-1.6v-5.437c0-.622.218-1.159.652-1.6.434-.44.962-.663 1.576-.663H5.72l2.273-5.192c.254-.58.64-1.01 1.159-1.282.32-.167.64-.246.953-.227.315.02.613.137.893.344.824.608 1.24 1.437 1.24 2.47v2.345h3.827zM9.741 3.063c-.247.128-.437.344-.57.647L6.708 9.34v7.683h6.655a2.59 2.59 0 0 0 1.72-.63c.498-.42.807-.955.926-1.601l1.02-5.544a.936.936 0 0 0-.225-.773.917.917 0 0 0-.739-.335h-4.557a.567.567 0 0 1-.562-.571V4.483c0-.591-.236-1.061-.708-1.41-.143-.105-.309-.109-.497-.01zm7.124 6.157a.766.766 0 0 0-.186-.636.753.753 0 0 0-.612-.276h-4.559a.734.734 0 0 1-.728-.74V4.483c0-.537-.21-.956-.64-1.273a.233.233 0 0 0-.137-.05.387.387 0 0 0-.185.053c-.208.108-.374.291-.495.566m0 0L6.874 9.375v7.479h6.489c.609 0 1.145-.197 1.614-.592.468-.396.757-.894.869-1.502l1.02-5.54M3.35 16.623c.*************.545.23h1.522V9.85H3.895a.732.732 0 0 0-.545.23.755.755 0 0 0-.226.553v5.437c0 .*************.553zm.545-6.941a.897.897 0 0 0-.663.278.924.924 0 0 0-.274.673v5.437c0 .263.091.487.274.673.183.186.404.28.663.28h1.688V9.681H3.895z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M18.137 9.447l.004-.02a2.044 2.044 0 0 0-.488-1.704c-.421-.491-.95-.733-1.588-.726h-2.282l1.584 1.142h.698a.917.917 0 0 1 .739.335.936.936 0 0 1 .225.773l-1.02 5.543a2.663 2.663 0 0 1-.926 1.602 2.59 2.59 0 0 1-1.72.63H6.708V9.34L9.171 3.71c.133-.303.323-.519.57-.647.188-.099.354-.095.497.01.472.349.708.819.708 1.41v.467l1.125.811V4.483c0-.98-.39-1.757-1.171-2.334-.517-.38-1.074-.415-1.673-.103-.482.252-.843.652-1.083 1.2L5.827 8.54H3.895c-.57 0-1.056.205-1.458.614a2.033 2.033 0 0 0-.604 1.48v5.437c0 .578.201 1.072.604 1.48.403.41.889.615 1.458.615h9.468c.918 0 1.731-.299 2.438-.895A3.774 3.774 0 0 0 17.114 15l1.023-5.553zm-14.242.235a.897.897 0 0 0-.663.278.924.924 0 0 0-.274.673v5.437c0 .263.091.487.274.673.183.186.404.28.663.28h1.688V9.681H3.895z" fill="currentColor"></path><path d="M10.946 7.568c0 .316.252.571.563.571h3.858l-1.584-1.142h-1.712V5.76l-1.125-.811v2.618z" fill="currentColor"></path></svg></div></div><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M18.304 10.543v.004a2.214 2.214 0 0 1-.526 1.84c-.452.528-1.028.792-1.714.784h-3.827v2.345c0 1.034-.416 1.863-1.24 2.471-.28.207-.578.324-.893.343-.314.02-.632-.06-.953-.226-.519-.271-.905-.702-1.159-1.282l-2.273-5.193H3.895a2.136 2.136 0 0 1-1.576-.663 2.203 2.203 0 0 1-.652-1.6V3.93c0-.623.218-1.16.652-1.6.434-.441.963-.663 1.576-.663h9.468c.957 0 1.807.311 2.544.933a3.943 3.943 0 0 1 1.37 2.368L18.3 10.52v.002l.004.02zm-1.19-5.544a3.774 3.774 0 0 0-1.313-2.27 3.673 3.673 0 0 0-2.438-.894H3.895c-.57 0-1.055.205-1.458.614a2.033 2.033 0 0 0-.604 1.48v5.437c0 .578.201 1.072.604 1.48.402.41.888.615 1.458.615h1.932l2.317 5.292c.24.549.6.949 1.083 1.2.599.313 1.156.279 1.672-.102.782-.577 1.172-1.355 1.172-2.335v-2.513h3.994c.638.007 1.167-.235 1.588-.727.421-.491.584-1.06.488-1.704l-.004-.02L17.114 5zM9.819 16.787c.**************.185.052a.233.233 0 0 0 .137-.05c.43-.316.64-.735.64-1.273v-3.084c0-.41.327-.74.729-.74h4.558a.752.752 0 0 0 .612-.277.766.766 0 0 0 .186-.635l-1.02-5.54a2.495 2.495 0 0 0-.869-1.502 2.426 2.426 0 0 0-1.613-.592H6.874v7.48l2.449 5.595m-2.615-5.56V2.978h6.655c.648 0 1.221.21 1.72.63.498.42.807.954.926 1.601l1.02 5.543a.936.936 0 0 1-.225.774.917.917 0 0 1-.739.335h-4.557a.567.567 0 0 0-.562.57v3.085c0 .592-.236 1.062-.708 1.41-.143.106-.309.11-.497.01-.247-.128-.437-.343-.57-.647l-2.463-5.628zM3.35 3.376a.754.754 0 0 0-.226.554v5.436c0 .*************.554.*************.545.23h1.522V3.146H3.895a.731.731 0 0 0-.545.23zm2.233-.398v7.34H3.895a.897.897 0 0 1-.663-.279.925.925 0 0 1-.274-.673V3.93c0-.263.091-.487.274-.673a.895.895 0 0 1 .663-.28h1.688zm4.235 13.809c-.208-.108-.375-.292-.495-.566l.495.566z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M15.801 2.73a3.774 3.774 0 0 1 1.313 2.269l1.023 5.554.004.019a2.044 2.044 0 0 1-.488 1.704c-.421.492-.95.734-1.588.727h-3.994v2.513c0 .98-.39 1.758-1.171 2.335-.517.38-1.074.415-1.673.102-.482-.251-.843-.651-1.083-1.2l-2.317-5.292H3.895c-.57 0-1.056-.205-1.458-.614a2.033 2.033 0 0 1-.604-1.48V3.93c0-.579.201-1.072.604-1.481.403-.41.889-.614 1.458-.614h9.468c.918 0 1.731.298 2.438.894zm-2.438.248c.648 0 1.221.21 1.72.63.498.42.807.954.926 1.601l1.02 5.543a.936.936 0 0 1-.225.774.917.917 0 0 1-.739.335h-4.557a.567.567 0 0 0-.562.57v3.085c0 .592-.236 1.062-.708 1.41-.143.106-.309.11-.497.01-.247-.128-.437-.343-.57-.647l-2.463-5.628V2.978h6.655zm-9.468 7.34a.897.897 0 0 1-.663-.279.925.925 0 0 1-.274-.673V3.93c0-.263.091-.487.274-.673a.895.895 0 0 1 .663-.28h1.688v7.341H3.895z" fill="currentColor"></path></svg></div></div></div><div style="flex: 1 1 0%;"></div></div></div><div class="_9663006"><div class="fbb737a4">那么LocalDateTime 与Date有区别吗</div><div class="e13328ad _3a26104"><div class="_425ea0b"><div class="ds-flex _78e0558 _0bbda35" style="align-items: flex-end; gap: 0px;"><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.65169 12.9243C3.68173 13.1045 3.74181 13.2748 3.80189 13.445C3.87198 13.6052 3.96211 13.7654 4.06225 13.9156C4.16238 14.0658 4.27253 14.206 4.4027 14.3362C4.52286 14.4663 4.66306 14.5765 4.81326 14.6766C4.96346 14.7768 5.11366 14.8569 5.28389 14.927C5.44411 14.9971 5.61434 15.0571 5.79459 15.0872C5.97483 15.1272 6.14506 15.1373 6.3253 15.1373V16.9196C6.30739 16.9196 6.28949 16.9195 6.27159 16.9193C5.9991 16.9158 5.72659 16.8859 5.4541 16.8295C5.16371 16.7694 4.88334 16.6893 4.61298 16.5692C4.3326 16.459 4.08226 16.3188 3.83193 16.1586C3.59161 15.9884 3.3613 15.7981 3.15102 15.5878C2.94074 15.3776 2.7605 15.1473 2.59027 14.9069C2.43006 14.6566 2.28986 14.3962 2.17972 14.1259C2.06957 13.8455 1.97944 13.5651 1.91936 13.2747C1.86929 12.9843 1.83926 12.684 1.83926 12.3936V6.26532C1.83926 5.96492 1.86929 5.67456 1.91936 5.38417C1.97944 5.09378 2.06957 4.80338 2.17972 4.53302C2.28986 4.26265 2.43006 4.0023 2.59027 3.75197C2.7605 3.50163 2.94074 3.27132 3.15102 3.06104C3.3613 2.85076 3.59161 2.67052 3.83193 2.50029C4.08226 2.33006 4.3326 2.19987 4.61298 2.07971C4.88334 1.96956 5.16371 1.87943 5.4541 1.81935C5.74449 1.75927 6.03491 1.73926 6.3253 1.73926H12.3934C12.6838 1.73926 12.9842 1.75927 13.2746 1.81935C13.555 1.87943 13.8354 1.96956 14.1158 2.07971C14.3861 2.19987 14.6465 2.33006 14.8868 2.50029C15.1371 2.67052 15.3574 2.85076 15.5677 3.06104C15.778 3.27132 15.9582 3.50163 16.1284 3.75197C16.2887 4.0023 16.4288 4.26265 16.539 4.53302C16.6592 4.80338 16.7393 5.09378 16.7994 5.38417C16.8558 5.65722 16.8858 5.93024 16.8892 6.21161C16.8894 6.22948 16.8895 6.24739 16.8895 6.26532H15.1271C15.1271 6.08508 15.1071 5.90486 15.067 5.72462C15.037 5.55439 14.9869 5.38415 14.9168 5.21392C14.8467 5.04369 14.7566 4.88347 14.6665 4.73327C14.5664 4.58307 14.4462 4.45289 14.326 4.32271C14.1959 4.19254 14.0557 4.08239 13.9055 3.98226C13.7553 3.88212 13.6051 3.79202 13.4348 3.72193C13.2746 3.65184 13.1044 3.60174 12.9242 3.5717C12.7539 3.53165 12.5737 3.51163 12.3934 3.51163H6.3253C6.14506 3.51163 5.97483 3.53165 5.79459 3.5717C5.61434 3.60174 5.44411 3.65184 5.28389 3.72193C5.11366 3.79202 4.96346 3.88212 4.81326 3.98226C4.66306 4.08239 4.52286 4.19254 4.4027 4.32271C4.27253 4.45289 4.16238 4.58307 4.06225 4.73327C3.96211 4.88347 3.87198 5.04369 3.80189 5.21392C3.74181 5.38415 3.68173 5.55439 3.65169 5.72462C3.61164 5.90486 3.60164 6.08508 3.60164 6.26532V12.3936C3.60164 12.5638 3.61164 12.744 3.65169 12.9243Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.66972 21.6772C9.39936 21.567 9.13902 21.4268 8.8987 21.2566C8.64836 21.0964 8.42804 20.9061 8.21776 20.6959C8.00748 20.4856 7.81723 20.2553 7.65701 20.015C7.4968 19.7646 7.3566 19.5043 7.24646 19.2239C7.12629 18.9535 7.04621 18.6731 6.98613 18.3727C6.92605 18.0823 6.89601 17.792 6.89601 17.4915V11.3733C6.89601 11.0729 6.92605 10.7825 6.98613 10.4922C7.04621 10.1918 7.12629 9.91137 7.24646 9.64101C7.3566 9.36063 7.4968 9.10028 7.65701 8.85996C7.81723 8.60962 8.00748 8.37931 8.21776 8.16903C8.42804 7.95875 8.64836 7.76849 8.8987 7.60828C9.13902 7.43805 9.39936 7.29785 9.66972 7.1877C9.94009 7.07755 10.2205 6.98745 10.5108 6.92737C10.8012 6.86729 11.0916 6.83725 11.392 6.83725H17.4602C17.7506 6.83725 18.041 6.86729 18.3313 6.92737C18.6217 6.98745 18.9021 7.07755 19.1725 7.1877C19.4529 7.29785 19.7032 7.43805 19.9535 7.60828C20.1938 7.76849 20.4242 7.95875 20.6345 8.16903C20.8447 8.37931 21.025 8.60962 21.1952 8.85996C21.3554 9.10028 21.4956 9.36063 21.6058 9.64101C21.7159 9.91137 21.806 10.1918 21.8661 10.4922C21.9162 10.7825 21.9462 11.0729 21.9462 11.3733V17.4915C21.9462 17.792 21.9162 18.0823 21.8661 18.3727C21.806 18.6731 21.7159 18.9535 21.6058 19.2239C21.4956 19.5043 21.3554 19.7646 21.1952 20.015C21.025 20.2553 20.8447 20.4856 20.6345 20.6959C20.4242 20.9061 20.1938 21.0964 19.9535 21.2566C19.7032 21.4268 19.4529 21.567 19.1725 21.6772C18.9021 21.7973 18.6217 21.8774 18.3313 21.9375C18.041 21.9976 17.7506 22.0276 17.4602 22.0276H11.392C11.0916 22.0276 10.8012 21.9976 10.5108 21.9375C10.2205 21.8774 9.94009 21.7973 9.66972 21.6772ZM10.8613 8.6697C11.0316 8.63966 11.2118 8.61965 11.392 8.61965H17.4602C17.6404 8.61965 17.8107 8.63966 17.9909 8.6697C18.1611 8.70975 18.3314 8.75983 18.5016 8.82992C18.6618 8.90001 18.822 8.98012 18.9722 9.08026C19.1224 9.18039 19.2626 9.30055 19.3828 9.42071C19.513 9.55088 19.6231 9.69109 19.7232 9.84129C19.8234 9.99149 19.9035 10.1517 19.9736 10.3219C20.0437 10.4821 20.0937 10.6624 20.1338 10.8326C20.1638 11.0129 20.1838 11.1931 20.1838 11.3733V17.4915C20.1838 17.6718 20.1638 17.852 20.1338 18.0323C20.0937 18.2125 20.0437 18.3828 19.9736 18.543C19.9035 18.7132 19.8234 18.8734 19.7232 19.0236C19.6231 19.1738 19.513 19.314 19.3828 19.4342C19.2626 19.5643 19.1224 19.6845 18.9722 19.7846C18.822 19.8848 18.6618 19.9649 18.5016 20.035C18.3314 20.1051 18.1611 20.1551 17.9909 20.1952C17.8107 20.2252 17.6404 20.2452 17.4602 20.2452H11.392C11.2118 20.2452 11.0316 20.2252 10.8613 20.1952C10.6811 20.1551 10.5108 20.1051 10.3506 20.035C10.1804 19.9649 10.0202 19.8848 9.87 19.7846C9.72982 19.6845 9.58962 19.5643 9.45945 19.4342C9.33929 19.314 9.21913 19.1738 9.119 19.0236C9.01886 18.8734 8.93875 18.7132 8.86866 18.543C8.79857 18.3828 8.74847 18.2125 8.71843 18.0323C8.67838 17.852 8.65836 17.6718 8.65836 17.4915V11.3733C8.65836 11.1931 8.67838 11.0129 8.71843 10.8326C8.74847 10.6624 8.79857 10.4821 8.86866 10.3219C8.93875 10.1517 9.01886 9.99149 9.119 9.84129C9.21913 9.69109 9.33929 9.55088 9.45945 9.42071C9.58962 9.30055 9.72982 9.18039 9.87 9.08026C10.0202 8.98012 10.1804 8.90001 10.3506 8.82992C10.5108 8.75983 10.6811 8.70975 10.8613 8.6697Z" fill="currentColor"></path></svg></div></div><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px; margin-left: 14px; opacity: 1;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.712 2.79a1.854 1.854 0 0 1 2.623 0l1.673 1.672a1.854 1.854 0 0 1 0 2.623l-9.714 9.714a2.91 2.91 0 0 1-2.058.853H1.945a.8.8 0 0 1-.8-.8v-2.29c0-.773.307-1.513.853-2.058l9.714-9.715zm1.492 1.13c-.1-.1-.261-.1-.361 0l-1.327 1.326 2.035 2.035 1.327-1.326c.1-.1.1-.262 0-.362L13.204 3.92zm-.783 4.491l-2.035-2.034-7.258 7.257a1.31 1.31 0 0 0-.384.927v1.492h1.492c.348 0 .681-.138.927-.384l7.258-7.258z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M17.772 17.608c.35 0 .633-.283.633-.633v-.492a.633.633 0 0 0-.633-.633H9.858L8.1 17.608h9.672z" fill="currentColor"></path></svg></div></div></div></div></div></div><div class="_4f9bf79 d7dc56a8 _43c05b5"><div class="_7eb2358 _58dfa60"><svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path id="path" d="M27.501 8.46875C27.249 8.3457 27.1406 8.58008 26.9932 8.69922C26.9434 8.73828 26.9004 8.78906 26.8584 8.83398C26.4902 9.22852 26.0605 9.48633 25.5 9.45508C24.6787 9.41016 23.9785 9.66797 23.3594 10.2969C23.2275 9.52148 22.79 9.05859 22.125 8.76172C21.7764 8.60742 21.4238 8.45312 21.1807 8.11719C21.0098 7.87891 20.9639 7.61328 20.8779 7.35156C20.8242 7.19336 20.7695 7.03125 20.5879 7.00391C20.3906 6.97266 20.3135 7.13867 20.2363 7.27734C19.9258 7.84375 19.8066 8.46875 19.8174 9.10156C19.8447 10.5234 20.4453 11.6562 21.6367 12.4629C21.7725 12.5547 21.8076 12.6484 21.7646 12.7832C21.6836 13.0605 21.5869 13.3301 21.501 13.6074C21.4473 13.7852 21.3662 13.8242 21.1768 13.7461C20.5225 13.4727 19.957 13.0684 19.458 12.5781C18.6104 11.7578 17.8438 10.8516 16.8877 10.1426C16.6631 9.97656 16.4395 9.82227 16.207 9.67578C15.2314 8.72656 16.335 7.94727 16.5898 7.85547C16.8574 7.75977 16.6826 7.42773 15.8193 7.43164C14.957 7.43555 14.167 7.72461 13.1611 8.10938C13.0137 8.16797 12.8594 8.21094 12.7002 8.24414C11.7871 8.07227 10.8389 8.0332 9.84766 8.14453C7.98242 8.35352 6.49219 9.23633 5.39648 10.7441C4.08105 12.5547 3.77148 14.6133 4.15039 16.7617C4.54883 19.0234 5.70215 20.8984 7.47559 22.3633C9.31348 23.8809 11.4307 24.625 13.8457 24.4824C15.3125 24.3984 16.9463 24.2012 18.7881 22.6406C19.2529 22.8711 19.7402 22.9629 20.5498 23.0332C21.1729 23.0918 21.7725 23.002 22.2373 22.9062C22.9648 22.752 22.9141 22.0781 22.6514 21.9531C20.5186 20.959 20.9863 21.3633 20.5605 21.0371C21.6445 19.752 23.2783 18.418 23.917 14.0977C23.9668 13.7539 23.9238 13.5391 23.917 13.2598C23.9131 13.0918 23.9512 13.0254 24.1445 13.0059C24.6787 12.9453 25.1973 12.7988 25.6738 12.5352C27.0557 11.7793 27.6123 10.5391 27.7441 9.05078C27.7637 8.82422 27.7402 8.58789 27.501 8.46875ZM15.46 21.8613C13.3926 20.2344 12.3906 19.6992 11.9766 19.7227C11.5898 19.7441 11.6592 20.1875 11.7441 20.4766C11.833 20.7617 11.9492 20.959 12.1123 21.209C12.2246 21.375 12.3018 21.623 12 21.8066C11.334 22.2207 10.1768 21.668 10.1221 21.6406C8.77539 20.8477 7.64941 19.7988 6.85547 18.3652C6.08984 16.9844 5.64453 15.5039 5.57129 13.9238C5.55176 13.541 5.66406 13.4062 6.04297 13.3379C6.54199 13.2461 7.05762 13.2266 7.55664 13.2988C9.66602 13.6074 11.4619 14.5527 12.9668 16.0469C13.8262 16.9004 14.4766 17.918 15.1465 18.9121C15.8584 19.9688 16.625 20.9746 17.6006 21.7988C17.9443 22.0879 18.2197 22.3086 18.4824 22.4707C17.6895 22.5586 16.3652 22.5781 15.46 21.8613ZM16.4502 15.4805C16.4502 15.3105 16.5859 15.1758 16.7568 15.1758C16.7949 15.1758 16.8301 15.1836 16.8613 15.1953C16.9033 15.2109 16.9424 15.2344 16.9727 15.2695C17.0273 15.3223 17.0586 15.4004 17.0586 15.4805C17.0586 15.6504 16.9229 15.7852 16.7529 15.7852C16.582 15.7852 16.4502 15.6504 16.4502 15.4805ZM19.5273 17.0625C19.3301 17.1426 19.1328 17.2129 18.9434 17.2207C18.6494 17.2344 18.3281 17.1152 18.1533 16.9688C17.8828 16.7422 17.6895 16.6152 17.6074 16.2168C17.5732 16.0469 17.5928 15.7852 17.623 15.6348C17.6934 15.3105 17.6152 15.1035 17.3877 14.9141C17.2012 14.7598 16.9658 14.7188 16.7061 14.7188C16.6094 14.7188 16.5205 14.6758 16.4541 14.6406C16.3457 14.5859 16.2568 14.4512 16.3418 14.2852C16.3691 14.2324 16.501 14.1016 16.5322 14.0781C16.8838 13.877 17.29 13.9434 17.666 14.0938C18.0146 14.2363 18.2773 14.498 18.6562 14.8672C19.0439 15.3145 19.1133 15.4395 19.334 15.7734C19.5078 16.0371 19.667 16.3066 19.7754 16.6152C19.8408 16.8066 19.7559 16.9648 19.5273 17.0625Z" fill-rule="nonzero" fill="#4D6BFE"></path></svg></div><div class="ds-markdown ds-markdown--block" style="--ds-md-zoom: 1.143;"><h1>LocalDateTime与Date的区别及MySQL插入方式</h1><h2>主要区别</h2><ol start="1"><li><p class="ds-markdown-paragraph"><strong>设计理念</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>java.util.Date</code>：表示特定的瞬间，精确到毫秒，包含日期、时间和时区信息（实际上时区信息存储在Calendar中）</p></li><li><p class="ds-markdown-paragraph"><code>LocalDateTime</code>：只表示日期和时间(年月日时分秒)，不包含时区信息</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>所属包和版本</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>：来自Java最初的版本，存在于java.util包</p></li><li><p class="ds-markdown-paragraph"><code>LocalDateTime</code>：来自Java 8的新日期时间API，存在于java.time包</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>可变性</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>是可变的(可以通过set方法修改)</p></li><li><p class="ds-markdown-paragraph"><code>LocalDateTime</code>是不可变的(任何修改都会返回新实例)</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>线程安全</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>不是线程安全的</p></li><li><p class="ds-markdown-paragraph"><code>LocalDateTime</code>是线程安全的</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>API设计</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>的API设计较差(很多方法已废弃)</p></li><li><p class="ds-markdown-paragraph"><code>LocalDateTime</code>提供了丰富且直观的日期时间操作方法</p></li></ul></li></ol><h2>功能对比</h2><div class="markdown-table-wrapper"><table><thead><tr><th>特性</th><th>Date</th><th>LocalDateTime</th></tr></thead><tbody><tr><td>包含时间信息</td><td>是</td><td>是</td></tr><tr><td>包含时区信息</td><td>是(通过Calendar间接实现)</td><td>否</td></tr><tr><td>精度</td><td>毫秒</td><td>纳秒</td></tr><tr><td>可变性</td><td>可变</td><td>不可变</td></tr><tr><td>线程安全</td><td>不安全</td><td>安全</td></tr><tr><td>格式化/解析</td><td>SimpleDateFormat</td><td>DateTimeFormatter</td></tr><tr><td>日期时间计算</td><td>不便</td><td>提供丰富方法(plusDays等)</td></tr></tbody></table></div><h2>插入MySQL时的区别</h2><ol start="1"><li><p class="ds-markdown-paragraph"><strong>对应MySQL类型</strong>：</p><ul><li><p class="ds-markdown-paragraph"><code>Date</code>通常对应MySQL的<code>DATETIME</code>或<code>TIMESTAMP</code></p></li><li><p class="ds-markdown-paragraph"><code>LocalDateTime</code>对应MySQL的<code>DATETIME</code>类型</p></li></ul></li><li><p class="ds-markdown-paragraph"><strong>JDBC处理</strong>：</p><ul><li><p class="ds-markdown-paragraph">对于<code>Date</code>：</p><div class="md-code-block md-code-block-light"><div class="md-code-block-banner-wrap"><div class="md-code-block-banner md-code-block-banner-lite"><div class="_121d384"><div class="d2a24f03"><span class="d813de27">java</span></div><div class="d2a24f03"><div class="efa13877"><div role="button" class="ds-button ds-button--secondary ds-button--borderless ds-button--rect ds-button--m _7db3914" tabindex="0" style="margin-right: 8px; font-size: 13px; height: 28px; padding: 0px 4px; --button-text-color: var(--dsr-text-2);"><div class="ds-button__icon"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.65169 12.9243C3.68173 13.1045 3.74181 13.2748 3.80189 13.445C3.87198 13.6052 3.96211 13.7654 4.06225 13.9156C4.16238 14.0658 4.27253 14.206 4.4027 14.3362C4.52286 14.4663 4.66306 14.5765 4.81326 14.6766C4.96346 14.7768 5.11366 14.8569 5.28389 14.927C5.44411 14.9971 5.61434 15.0571 5.79459 15.0872C5.97483 15.1272 6.14506 15.1373 6.3253 15.1373V16.9196C6.30739 16.9196 6.28949 16.9195 6.27159 16.9193C5.9991 16.9158 5.72659 16.8859 5.4541 16.8295C5.16371 16.7694 4.88334 16.6893 4.61298 16.5692C4.3326 16.459 4.08226 16.3188 3.83193 16.1586C3.59161 15.9884 3.3613 15.7981 3.15102 15.5878C2.94074 15.3776 2.7605 15.1473 2.59027 14.9069C2.43006 14.6566 2.28986 14.3962 2.17972 14.1259C2.06957 13.8455 1.97944 13.5651 1.91936 13.2747C1.86929 12.9843 1.83926 12.684 1.83926 12.3936V6.26532C1.83926 5.96492 1.86929 5.67456 1.91936 5.38417C1.97944 5.09378 2.06957 4.80338 2.17972 4.53302C2.28986 4.26265 2.43006 4.0023 2.59027 3.75197C2.7605 3.50163 2.94074 3.27132 3.15102 3.06104C3.3613 2.85076 3.59161 2.67052 3.83193 2.50029C4.08226 2.33006 4.3326 2.19987 4.61298 2.07971C4.88334 1.96956 5.16371 1.87943 5.4541 1.81935C5.74449 1.75927 6.03491 1.73926 6.3253 1.73926H12.3934C12.6838 1.73926 12.9842 1.75927 13.2746 1.81935C13.555 1.87943 13.8354 1.96956 14.1158 2.07971C14.3861 2.19987 14.6465 2.33006 14.8868 2.50029C15.1371 2.67052 15.3574 2.85076 15.5677 3.06104C15.778 3.27132 15.9582 3.50163 16.1284 3.75197C16.2887 4.0023 16.4288 4.26265 16.539 4.53302C16.6592 4.80338 16.7393 5.09378 16.7994 5.38417C16.8558 5.65722 16.8858 5.93024 16.8892 6.21161C16.8894 6.22948 16.8895 6.24739 16.8895 6.26532H15.1271C15.1271 6.08508 15.1071 5.90486 15.067 5.72462C15.037 5.55439 14.9869 5.38415 14.9168 5.21392C14.8467 5.04369 14.7566 4.88347 14.6665 4.73327C14.5664 4.58307 14.4462 4.45289 14.326 4.32271C14.1959 4.19254 14.0557 4.08239 13.9055 3.98226C13.7553 3.88212 13.6051 3.79202 13.4348 3.72193C13.2746 3.65184 13.1044 3.60174 12.9242 3.5717C12.7539 3.53165 12.5737 3.51163 12.3934 3.51163H6.3253C6.14506 3.51163 5.97483 3.53165 5.79459 3.5717C5.61434 3.60174 5.44411 3.65184 5.28389 3.72193C5.11366 3.79202 4.96346 3.88212 4.81326 3.98226C4.66306 4.08239 4.52286 4.19254 4.4027 4.32271C4.27253 4.45289 4.16238 4.58307 4.06225 4.73327C3.96211 4.88347 3.87198 5.04369 3.80189 5.21392C3.74181 5.38415 3.68173 5.55439 3.65169 5.72462C3.61164 5.90486 3.60164 6.08508 3.60164 6.26532V12.3936C3.60164 12.5638 3.61164 12.744 3.65169 12.9243Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.66972 21.6772C9.39936 21.567 9.13902 21.4268 8.8987 21.2566C8.64836 21.0964 8.42804 20.9061 8.21776 20.6959C8.00748 20.4856 7.81723 20.2553 7.65701 20.015C7.4968 19.7646 7.3566 19.5043 7.24646 19.2239C7.12629 18.9535 7.04621 18.6731 6.98613 18.3727C6.92605 18.0823 6.89601 17.792 6.89601 17.4915V11.3733C6.89601 11.0729 6.92605 10.7825 6.98613 10.4922C7.04621 10.1918 7.12629 9.91137 7.24646 9.64101C7.3566 9.36063 7.4968 9.10028 7.65701 8.85996C7.81723 8.60962 8.00748 8.37931 8.21776 8.16903C8.42804 7.95875 8.64836 7.76849 8.8987 7.60828C9.13902 7.43805 9.39936 7.29785 9.66972 7.1877C9.94009 7.07755 10.2205 6.98745 10.5108 6.92737C10.8012 6.86729 11.0916 6.83725 11.392 6.83725H17.4602C17.7506 6.83725 18.041 6.86729 18.3313 6.92737C18.6217 6.98745 18.9021 7.07755 19.1725 7.1877C19.4529 7.29785 19.7032 7.43805 19.9535 7.60828C20.1938 7.76849 20.4242 7.95875 20.6345 8.16903C20.8447 8.37931 21.025 8.60962 21.1952 8.85996C21.3554 9.10028 21.4956 9.36063 21.6058 9.64101C21.7159 9.91137 21.806 10.1918 21.8661 10.4922C21.9162 10.7825 21.9462 11.0729 21.9462 11.3733V17.4915C21.9462 17.792 21.9162 18.0823 21.8661 18.3727C21.806 18.6731 21.7159 18.9535 21.6058 19.2239C21.4956 19.5043 21.3554 19.7646 21.1952 20.015C21.025 20.2553 20.8447 20.4856 20.6345 20.6959C20.4242 20.9061 20.1938 21.0964 19.9535 21.2566C19.7032 21.4268 19.4529 21.567 19.1725 21.6772C18.9021 21.7973 18.6217 21.8774 18.3313 21.9375C18.041 21.9976 17.7506 22.0276 17.4602 22.0276H11.392C11.0916 22.0276 10.8012 21.9976 10.5108 21.9375C10.2205 21.8774 9.94009 21.7973 9.66972 21.6772ZM10.8613 8.6697C11.0316 8.63966 11.2118 8.61965 11.392 8.61965H17.4602C17.6404 8.61965 17.8107 8.63966 17.9909 8.6697C18.1611 8.70975 18.3314 8.75983 18.5016 8.82992C18.6618 8.90001 18.822 8.98012 18.9722 9.08026C19.1224 9.18039 19.2626 9.30055 19.3828 9.42071C19.513 9.55088 19.6231 9.69109 19.7232 9.84129C19.8234 9.99149 19.9035 10.1517 19.9736 10.3219C20.0437 10.4821 20.0937 10.6624 20.1338 10.8326C20.1638 11.0129 20.1838 11.1931 20.1838 11.3733V17.4915C20.1838 17.6718 20.1638 17.852 20.1338 18.0323C20.0937 18.2125 20.0437 18.3828 19.9736 18.543C19.9035 18.7132 19.8234 18.8734 19.7232 19.0236C19.6231 19.1738 19.513 19.314 19.3828 19.4342C19.2626 19.5643 19.1224 19.6845 18.9722 19.7846C18.822 19.8848 18.6618 19.9649 18.5016 20.035C18.3314 20.1051 18.1611 20.1551 17.9909 20.1952C17.8107 20.2252 17.6404 20.2452 17.4602 20.2452H11.392C11.2118 20.2452 11.0316 20.2252 10.8613 20.1952C10.6811 20.1551 10.5108 20.1051 10.3506 20.035C10.1804 19.9649 10.0202 19.8848 9.87 19.7846C9.72982 19.6845 9.58962 19.5643 9.45945 19.4342C9.33929 19.314 9.21913 19.1738 9.119 19.0236C9.01886 18.8734 8.93875 18.7132 8.86866 18.543C8.79857 18.3828 8.74847 18.2125 8.71843 18.0323C8.67838 17.852 8.65836 17.6718 8.65836 17.4915V11.3733C8.65836 11.1931 8.67838 11.0129 8.71843 10.8326C8.74847 10.6624 8.79857 10.4821 8.86866 10.3219C8.93875 10.1517 9.01886 9.99149 9.119 9.84129C9.21913 9.69109 9.33929 9.55088 9.45945 9.42071C9.58962 9.30055 9.72982 9.18039 9.87 9.08026C10.0202 8.98012 10.1804 8.90001 10.3506 8.82992C10.5108 8.75983 10.6811 8.70975 10.8613 8.6697Z" fill="currentColor"></path></svg></div></div><span class="code-info-button-text">复制</span></div><div role="button" class="ds-button ds-button--secondary ds-button--borderless ds-button--rect ds-button--s _7db3914" tabindex="0" style="padding: 0px 4px; height: 28px; --button-text-color: var(--dsr-text-2); --button-font-size: 13px;"><div class="ds-button__icon"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 2.55a.97.97 0 0 1 .982.956v13.04a.97.97 0 0 1-.982.957.97.97 0 0 1-.982-.956V3.507A.97.97 0 0 1 12 2.55z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M19.418 9.808c.382.375.37.971-.027 1.332l-6.7 6.085a1.04 1.04 0 0 1-1.41-.025.905.905 0 0 1 .027-1.332l6.7-6.085a1.04 1.04 0 0 1 1.41.025z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M4.582 9.808a1.04 1.04 0 0 1 1.41-.025l6.7 6.085c.397.361.409.957.027 1.332a1.04 1.04 0 0 1-1.41.025l-6.7-6.085a.905.905 0 0 1-.027-1.332z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M3.068 16.46a.97.97 0 0 1 .983.956v1.739c0 .432.36.782.803.782h14.291c.445 0 .804-.35.804-.782v-1.739a.97.97 0 0 1 .983-.956.97.97 0 0 1 .982.956v1.739c0 1.488-1.24 2.695-2.769 2.695H4.855c-1.53 0-2.77-1.207-2.77-2.695v-1.739a.97.97 0 0 1 .983-.956z" fill="currentColor"></path></svg></div></div><span class="code-info-button-text">下载</span></div></div></div></div></div></div><pre><span class="token class-name">PreparedStatement</span> stmt <span class="token operator">=</span> conn<span class="token punctuation">.</span><span class="token function">prepareStatement</span><span class="token punctuation">(</span><span class="token string">"INSERT INTO table(dt) VALUES (?)"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
stmt<span class="token punctuation">.</span><span class="token function">setTimestamp</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token keyword">new</span> <span class="token class-name">Timestamp</span><span class="token punctuation">(</span>date<span class="token punctuation">.</span><span class="token function">getTime</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span></pre></div></li><li><p class="ds-markdown-paragraph">对于<code>LocalDateTime</code>：</p><div class="md-code-block md-code-block-light"><div class="md-code-block-banner-wrap"><div class="md-code-block-banner md-code-block-banner-lite"><div class="_121d384"><div class="d2a24f03"><span class="d813de27">java</span></div><div class="d2a24f03"><div class="efa13877"><div role="button" class="ds-button ds-button--secondary ds-button--borderless ds-button--rect ds-button--m _7db3914" tabindex="0" style="margin-right: 8px; font-size: 13px; height: 28px; padding: 0px 4px; --button-text-color: var(--dsr-text-2);"><div class="ds-button__icon"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.65169 12.9243C3.68173 13.1045 3.74181 13.2748 3.80189 13.445C3.87198 13.6052 3.96211 13.7654 4.06225 13.9156C4.16238 14.0658 4.27253 14.206 4.4027 14.3362C4.52286 14.4663 4.66306 14.5765 4.81326 14.6766C4.96346 14.7768 5.11366 14.8569 5.28389 14.927C5.44411 14.9971 5.61434 15.0571 5.79459 15.0872C5.97483 15.1272 6.14506 15.1373 6.3253 15.1373V16.9196C6.30739 16.9196 6.28949 16.9195 6.27159 16.9193C5.9991 16.9158 5.72659 16.8859 5.4541 16.8295C5.16371 16.7694 4.88334 16.6893 4.61298 16.5692C4.3326 16.459 4.08226 16.3188 3.83193 16.1586C3.59161 15.9884 3.3613 15.7981 3.15102 15.5878C2.94074 15.3776 2.7605 15.1473 2.59027 14.9069C2.43006 14.6566 2.28986 14.3962 2.17972 14.1259C2.06957 13.8455 1.97944 13.5651 1.91936 13.2747C1.86929 12.9843 1.83926 12.684 1.83926 12.3936V6.26532C1.83926 5.96492 1.86929 5.67456 1.91936 5.38417C1.97944 5.09378 2.06957 4.80338 2.17972 4.53302C2.28986 4.26265 2.43006 4.0023 2.59027 3.75197C2.7605 3.50163 2.94074 3.27132 3.15102 3.06104C3.3613 2.85076 3.59161 2.67052 3.83193 2.50029C4.08226 2.33006 4.3326 2.19987 4.61298 2.07971C4.88334 1.96956 5.16371 1.87943 5.4541 1.81935C5.74449 1.75927 6.03491 1.73926 6.3253 1.73926H12.3934C12.6838 1.73926 12.9842 1.75927 13.2746 1.81935C13.555 1.87943 13.8354 1.96956 14.1158 2.07971C14.3861 2.19987 14.6465 2.33006 14.8868 2.50029C15.1371 2.67052 15.3574 2.85076 15.5677 3.06104C15.778 3.27132 15.9582 3.50163 16.1284 3.75197C16.2887 4.0023 16.4288 4.26265 16.539 4.53302C16.6592 4.80338 16.7393 5.09378 16.7994 5.38417C16.8558 5.65722 16.8858 5.93024 16.8892 6.21161C16.8894 6.22948 16.8895 6.24739 16.8895 6.26532H15.1271C15.1271 6.08508 15.1071 5.90486 15.067 5.72462C15.037 5.55439 14.9869 5.38415 14.9168 5.21392C14.8467 5.04369 14.7566 4.88347 14.6665 4.73327C14.5664 4.58307 14.4462 4.45289 14.326 4.32271C14.1959 4.19254 14.0557 4.08239 13.9055 3.98226C13.7553 3.88212 13.6051 3.79202 13.4348 3.72193C13.2746 3.65184 13.1044 3.60174 12.9242 3.5717C12.7539 3.53165 12.5737 3.51163 12.3934 3.51163H6.3253C6.14506 3.51163 5.97483 3.53165 5.79459 3.5717C5.61434 3.60174 5.44411 3.65184 5.28389 3.72193C5.11366 3.79202 4.96346 3.88212 4.81326 3.98226C4.66306 4.08239 4.52286 4.19254 4.4027 4.32271C4.27253 4.45289 4.16238 4.58307 4.06225 4.73327C3.96211 4.88347 3.87198 5.04369 3.80189 5.21392C3.74181 5.38415 3.68173 5.55439 3.65169 5.72462C3.61164 5.90486 3.60164 6.08508 3.60164 6.26532V12.3936C3.60164 12.5638 3.61164 12.744 3.65169 12.9243Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.66972 21.6772C9.39936 21.567 9.13902 21.4268 8.8987 21.2566C8.64836 21.0964 8.42804 20.9061 8.21776 20.6959C8.00748 20.4856 7.81723 20.2553 7.65701 20.015C7.4968 19.7646 7.3566 19.5043 7.24646 19.2239C7.12629 18.9535 7.04621 18.6731 6.98613 18.3727C6.92605 18.0823 6.89601 17.792 6.89601 17.4915V11.3733C6.89601 11.0729 6.92605 10.7825 6.98613 10.4922C7.04621 10.1918 7.12629 9.91137 7.24646 9.64101C7.3566 9.36063 7.4968 9.10028 7.65701 8.85996C7.81723 8.60962 8.00748 8.37931 8.21776 8.16903C8.42804 7.95875 8.64836 7.76849 8.8987 7.60828C9.13902 7.43805 9.39936 7.29785 9.66972 7.1877C9.94009 7.07755 10.2205 6.98745 10.5108 6.92737C10.8012 6.86729 11.0916 6.83725 11.392 6.83725H17.4602C17.7506 6.83725 18.041 6.86729 18.3313 6.92737C18.6217 6.98745 18.9021 7.07755 19.1725 7.1877C19.4529 7.29785 19.7032 7.43805 19.9535 7.60828C20.1938 7.76849 20.4242 7.95875 20.6345 8.16903C20.8447 8.37931 21.025 8.60962 21.1952 8.85996C21.3554 9.10028 21.4956 9.36063 21.6058 9.64101C21.7159 9.91137 21.806 10.1918 21.8661 10.4922C21.9162 10.7825 21.9462 11.0729 21.9462 11.3733V17.4915C21.9462 17.792 21.9162 18.0823 21.8661 18.3727C21.806 18.6731 21.7159 18.9535 21.6058 19.2239C21.4956 19.5043 21.3554 19.7646 21.1952 20.015C21.025 20.2553 20.8447 20.4856 20.6345 20.6959C20.4242 20.9061 20.1938 21.0964 19.9535 21.2566C19.7032 21.4268 19.4529 21.567 19.1725 21.6772C18.9021 21.7973 18.6217 21.8774 18.3313 21.9375C18.041 21.9976 17.7506 22.0276 17.4602 22.0276H11.392C11.0916 22.0276 10.8012 21.9976 10.5108 21.9375C10.2205 21.8774 9.94009 21.7973 9.66972 21.6772ZM10.8613 8.6697C11.0316 8.63966 11.2118 8.61965 11.392 8.61965H17.4602C17.6404 8.61965 17.8107 8.63966 17.9909 8.6697C18.1611 8.70975 18.3314 8.75983 18.5016 8.82992C18.6618 8.90001 18.822 8.98012 18.9722 9.08026C19.1224 9.18039 19.2626 9.30055 19.3828 9.42071C19.513 9.55088 19.6231 9.69109 19.7232 9.84129C19.8234 9.99149 19.9035 10.1517 19.9736 10.3219C20.0437 10.4821 20.0937 10.6624 20.1338 10.8326C20.1638 11.0129 20.1838 11.1931 20.1838 11.3733V17.4915C20.1838 17.6718 20.1638 17.852 20.1338 18.0323C20.0937 18.2125 20.0437 18.3828 19.9736 18.543C19.9035 18.7132 19.8234 18.8734 19.7232 19.0236C19.6231 19.1738 19.513 19.314 19.3828 19.4342C19.2626 19.5643 19.1224 19.6845 18.9722 19.7846C18.822 19.8848 18.6618 19.9649 18.5016 20.035C18.3314 20.1051 18.1611 20.1551 17.9909 20.1952C17.8107 20.2252 17.6404 20.2452 17.4602 20.2452H11.392C11.2118 20.2452 11.0316 20.2252 10.8613 20.1952C10.6811 20.1551 10.5108 20.1051 10.3506 20.035C10.1804 19.9649 10.0202 19.8848 9.87 19.7846C9.72982 19.6845 9.58962 19.5643 9.45945 19.4342C9.33929 19.314 9.21913 19.1738 9.119 19.0236C9.01886 18.8734 8.93875 18.7132 8.86866 18.543C8.79857 18.3828 8.74847 18.2125 8.71843 18.0323C8.67838 17.852 8.65836 17.6718 8.65836 17.4915V11.3733C8.65836 11.1931 8.67838 11.0129 8.71843 10.8326C8.74847 10.6624 8.79857 10.4821 8.86866 10.3219C8.93875 10.1517 9.01886 9.99149 9.119 9.84129C9.21913 9.69109 9.33929 9.55088 9.45945 9.42071C9.58962 9.30055 9.72982 9.18039 9.87 9.08026C10.0202 8.98012 10.1804 8.90001 10.3506 8.82992C10.5108 8.75983 10.6811 8.70975 10.8613 8.6697Z" fill="currentColor"></path></svg></div></div><span class="code-info-button-text">复制</span></div><div role="button" class="ds-button ds-button--secondary ds-button--borderless ds-button--rect ds-button--s _7db3914" tabindex="0" style="padding: 0px 4px; height: 28px; --button-text-color: var(--dsr-text-2); --button-font-size: 13px;"><div class="ds-button__icon"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 2.55a.97.97 0 0 1 .982.956v13.04a.97.97 0 0 1-.982.957.97.97 0 0 1-.982-.956V3.507A.97.97 0 0 1 12 2.55z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M19.418 9.808c.382.375.37.971-.027 1.332l-6.7 6.085a1.04 1.04 0 0 1-1.41-.025.905.905 0 0 1 .027-1.332l6.7-6.085a1.04 1.04 0 0 1 1.41.025z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M4.582 9.808a1.04 1.04 0 0 1 1.41-.025l6.7 6.085c.397.361.409.957.027 1.332a1.04 1.04 0 0 1-1.41.025l-6.7-6.085a.905.905 0 0 1-.027-1.332z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M3.068 16.46a.97.97 0 0 1 .983.956v1.739c0 .432.36.782.803.782h14.291c.445 0 .804-.35.804-.782v-1.739a.97.97 0 0 1 .983-.956.97.97 0 0 1 .982.956v1.739c0 1.488-1.24 2.695-2.769 2.695H4.855c-1.53 0-2.77-1.207-2.77-2.695v-1.739a.97.97 0 0 1 .983-.956z" fill="currentColor"></path></svg></div></div><span class="code-info-button-text">下载</span></div></div></div></div></div></div><pre><span class="token class-name">PreparedStatement</span> stmt <span class="token operator">=</span> conn<span class="token punctuation">.</span><span class="token function">prepareStatement</span><span class="token punctuation">(</span><span class="token string">"INSERT INTO table(dt) VALUES (?)"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
stmt<span class="token punctuation">.</span><span class="token function">setObject</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> localDateTime<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// 或者转换为Timestamp</span>
stmt<span class="token punctuation">.</span><span class="token function">setTimestamp</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token class-name">Timestamp</span><span class="token punctuation">.</span><span class="token function">valueOf</span><span class="token punctuation">(</span>localDateTime<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span></pre></div></li></ul></li></ol><h2>示例代码</h2><div class="md-code-block md-code-block-light"><div class="md-code-block-banner-wrap"><div class="md-code-block-banner md-code-block-banner-lite"><div class="_121d384"><div class="d2a24f03"><span class="d813de27">java</span></div><div class="d2a24f03"><div class="efa13877"><div role="button" class="ds-button ds-button--secondary ds-button--borderless ds-button--rect ds-button--m _7db3914" tabindex="0" style="margin-right: 8px; font-size: 13px; height: 28px; padding: 0px 4px; --button-text-color: var(--dsr-text-2);"><div class="ds-button__icon"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.65169 12.9243C3.68173 13.1045 3.74181 13.2748 3.80189 13.445C3.87198 13.6052 3.96211 13.7654 4.06225 13.9156C4.16238 14.0658 4.27253 14.206 4.4027 14.3362C4.52286 14.4663 4.66306 14.5765 4.81326 14.6766C4.96346 14.7768 5.11366 14.8569 5.28389 14.927C5.44411 14.9971 5.61434 15.0571 5.79459 15.0872C5.97483 15.1272 6.14506 15.1373 6.3253 15.1373V16.9196C6.30739 16.9196 6.28949 16.9195 6.27159 16.9193C5.9991 16.9158 5.72659 16.8859 5.4541 16.8295C5.16371 16.7694 4.88334 16.6893 4.61298 16.5692C4.3326 16.459 4.08226 16.3188 3.83193 16.1586C3.59161 15.9884 3.3613 15.7981 3.15102 15.5878C2.94074 15.3776 2.7605 15.1473 2.59027 14.9069C2.43006 14.6566 2.28986 14.3962 2.17972 14.1259C2.06957 13.8455 1.97944 13.5651 1.91936 13.2747C1.86929 12.9843 1.83926 12.684 1.83926 12.3936V6.26532C1.83926 5.96492 1.86929 5.67456 1.91936 5.38417C1.97944 5.09378 2.06957 4.80338 2.17972 4.53302C2.28986 4.26265 2.43006 4.0023 2.59027 3.75197C2.7605 3.50163 2.94074 3.27132 3.15102 3.06104C3.3613 2.85076 3.59161 2.67052 3.83193 2.50029C4.08226 2.33006 4.3326 2.19987 4.61298 2.07971C4.88334 1.96956 5.16371 1.87943 5.4541 1.81935C5.74449 1.75927 6.03491 1.73926 6.3253 1.73926H12.3934C12.6838 1.73926 12.9842 1.75927 13.2746 1.81935C13.555 1.87943 13.8354 1.96956 14.1158 2.07971C14.3861 2.19987 14.6465 2.33006 14.8868 2.50029C15.1371 2.67052 15.3574 2.85076 15.5677 3.06104C15.778 3.27132 15.9582 3.50163 16.1284 3.75197C16.2887 4.0023 16.4288 4.26265 16.539 4.53302C16.6592 4.80338 16.7393 5.09378 16.7994 5.38417C16.8558 5.65722 16.8858 5.93024 16.8892 6.21161C16.8894 6.22948 16.8895 6.24739 16.8895 6.26532H15.1271C15.1271 6.08508 15.1071 5.90486 15.067 5.72462C15.037 5.55439 14.9869 5.38415 14.9168 5.21392C14.8467 5.04369 14.7566 4.88347 14.6665 4.73327C14.5664 4.58307 14.4462 4.45289 14.326 4.32271C14.1959 4.19254 14.0557 4.08239 13.9055 3.98226C13.7553 3.88212 13.6051 3.79202 13.4348 3.72193C13.2746 3.65184 13.1044 3.60174 12.9242 3.5717C12.7539 3.53165 12.5737 3.51163 12.3934 3.51163H6.3253C6.14506 3.51163 5.97483 3.53165 5.79459 3.5717C5.61434 3.60174 5.44411 3.65184 5.28389 3.72193C5.11366 3.79202 4.96346 3.88212 4.81326 3.98226C4.66306 4.08239 4.52286 4.19254 4.4027 4.32271C4.27253 4.45289 4.16238 4.58307 4.06225 4.73327C3.96211 4.88347 3.87198 5.04369 3.80189 5.21392C3.74181 5.38415 3.68173 5.55439 3.65169 5.72462C3.61164 5.90486 3.60164 6.08508 3.60164 6.26532V12.3936C3.60164 12.5638 3.61164 12.744 3.65169 12.9243Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.66972 21.6772C9.39936 21.567 9.13902 21.4268 8.8987 21.2566C8.64836 21.0964 8.42804 20.9061 8.21776 20.6959C8.00748 20.4856 7.81723 20.2553 7.65701 20.015C7.4968 19.7646 7.3566 19.5043 7.24646 19.2239C7.12629 18.9535 7.04621 18.6731 6.98613 18.3727C6.92605 18.0823 6.89601 17.792 6.89601 17.4915V11.3733C6.89601 11.0729 6.92605 10.7825 6.98613 10.4922C7.04621 10.1918 7.12629 9.91137 7.24646 9.64101C7.3566 9.36063 7.4968 9.10028 7.65701 8.85996C7.81723 8.60962 8.00748 8.37931 8.21776 8.16903C8.42804 7.95875 8.64836 7.76849 8.8987 7.60828C9.13902 7.43805 9.39936 7.29785 9.66972 7.1877C9.94009 7.07755 10.2205 6.98745 10.5108 6.92737C10.8012 6.86729 11.0916 6.83725 11.392 6.83725H17.4602C17.7506 6.83725 18.041 6.86729 18.3313 6.92737C18.6217 6.98745 18.9021 7.07755 19.1725 7.1877C19.4529 7.29785 19.7032 7.43805 19.9535 7.60828C20.1938 7.76849 20.4242 7.95875 20.6345 8.16903C20.8447 8.37931 21.025 8.60962 21.1952 8.85996C21.3554 9.10028 21.4956 9.36063 21.6058 9.64101C21.7159 9.91137 21.806 10.1918 21.8661 10.4922C21.9162 10.7825 21.9462 11.0729 21.9462 11.3733V17.4915C21.9462 17.792 21.9162 18.0823 21.8661 18.3727C21.806 18.6731 21.7159 18.9535 21.6058 19.2239C21.4956 19.5043 21.3554 19.7646 21.1952 20.015C21.025 20.2553 20.8447 20.4856 20.6345 20.6959C20.4242 20.9061 20.1938 21.0964 19.9535 21.2566C19.7032 21.4268 19.4529 21.567 19.1725 21.6772C18.9021 21.7973 18.6217 21.8774 18.3313 21.9375C18.041 21.9976 17.7506 22.0276 17.4602 22.0276H11.392C11.0916 22.0276 10.8012 21.9976 10.5108 21.9375C10.2205 21.8774 9.94009 21.7973 9.66972 21.6772ZM10.8613 8.6697C11.0316 8.63966 11.2118 8.61965 11.392 8.61965H17.4602C17.6404 8.61965 17.8107 8.63966 17.9909 8.6697C18.1611 8.70975 18.3314 8.75983 18.5016 8.82992C18.6618 8.90001 18.822 8.98012 18.9722 9.08026C19.1224 9.18039 19.2626 9.30055 19.3828 9.42071C19.513 9.55088 19.6231 9.69109 19.7232 9.84129C19.8234 9.99149 19.9035 10.1517 19.9736 10.3219C20.0437 10.4821 20.0937 10.6624 20.1338 10.8326C20.1638 11.0129 20.1838 11.1931 20.1838 11.3733V17.4915C20.1838 17.6718 20.1638 17.852 20.1338 18.0323C20.0937 18.2125 20.0437 18.3828 19.9736 18.543C19.9035 18.7132 19.8234 18.8734 19.7232 19.0236C19.6231 19.1738 19.513 19.314 19.3828 19.4342C19.2626 19.5643 19.1224 19.6845 18.9722 19.7846C18.822 19.8848 18.6618 19.9649 18.5016 20.035C18.3314 20.1051 18.1611 20.1551 17.9909 20.1952C17.8107 20.2252 17.6404 20.2452 17.4602 20.2452H11.392C11.2118 20.2452 11.0316 20.2252 10.8613 20.1952C10.6811 20.1551 10.5108 20.1051 10.3506 20.035C10.1804 19.9649 10.0202 19.8848 9.87 19.7846C9.72982 19.6845 9.58962 19.5643 9.45945 19.4342C9.33929 19.314 9.21913 19.1738 9.119 19.0236C9.01886 18.8734 8.93875 18.7132 8.86866 18.543C8.79857 18.3828 8.74847 18.2125 8.71843 18.0323C8.67838 17.852 8.65836 17.6718 8.65836 17.4915V11.3733C8.65836 11.1931 8.67838 11.0129 8.71843 10.8326C8.74847 10.6624 8.79857 10.4821 8.86866 10.3219C8.93875 10.1517 9.01886 9.99149 9.119 9.84129C9.21913 9.69109 9.33929 9.55088 9.45945 9.42071C9.58962 9.30055 9.72982 9.18039 9.87 9.08026C10.0202 8.98012 10.1804 8.90001 10.3506 8.82992C10.5108 8.75983 10.6811 8.70975 10.8613 8.6697Z" fill="currentColor"></path></svg></div></div><span class="code-info-button-text">复制</span></div><div role="button" class="ds-button ds-button--secondary ds-button--borderless ds-button--rect ds-button--s _7db3914" tabindex="0" style="padding: 0px 4px; height: 28px; --button-text-color: var(--dsr-text-2); --button-font-size: 13px;"><div class="ds-button__icon"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 2.55a.97.97 0 0 1 .982.956v13.04a.97.97 0 0 1-.982.957.97.97 0 0 1-.982-.956V3.507A.97.97 0 0 1 12 2.55z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M19.418 9.808c.382.375.37.971-.027 1.332l-6.7 6.085a1.04 1.04 0 0 1-1.41-.025.905.905 0 0 1 .027-1.332l6.7-6.085a1.04 1.04 0 0 1 1.41.025z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M4.582 9.808a1.04 1.04 0 0 1 1.41-.025l6.7 6.085c.397.361.409.957.027 1.332a1.04 1.04 0 0 1-1.41.025l-6.7-6.085a.905.905 0 0 1-.027-1.332z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M3.068 16.46a.97.97 0 0 1 .983.956v1.739c0 .432.36.782.803.782h14.291c.445 0 .804-.35.804-.782v-1.739a.97.97 0 0 1 .983-.956.97.97 0 0 1 .982.956v1.739c0 1.488-1.24 2.695-2.769 2.695H4.855c-1.53 0-2.77-1.207-2.77-2.695v-1.739a.97.97 0 0 1 .983-.956z" fill="currentColor"></path></svg></div></div><span class="code-info-button-text">下载</span></div></div></div></div></div></div><pre><span class="token comment">// 使用Date插入MySQL</span>
<span class="token class-name">Date</span> utilDate <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Date</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token class-name">PreparedStatement</span> stmt1 <span class="token operator">=</span> connection<span class="token punctuation">.</span><span class="token function">prepareStatement</span><span class="token punctuation">(</span><span class="token string">"INSERT INTO table(datetime_col) VALUES (?)"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
stmt1<span class="token punctuation">.</span><span class="token function">setTimestamp</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token keyword">new</span> <span class="token class-name">Timestamp</span><span class="token punctuation">(</span>utilDate<span class="token punctuation">.</span><span class="token function">getTime</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 使用LocalDateTime插入MySQL</span>
<span class="token class-name">LocalDateTime</span> localDateTime <span class="token operator">=</span> <span class="token class-name">LocalDateTime</span><span class="token punctuation">.</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token class-name">PreparedStatement</span> stmt2 <span class="token operator">=</span> connection<span class="token punctuation">.</span><span class="token function">prepareStatement</span><span class="token punctuation">(</span><span class="token string">"INSERT INTO table(datetime_col) VALUES (?)"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
stmt2<span class="token punctuation">.</span><span class="token function">setObject</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> localDateTime<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// 或者</span>
stmt2<span class="token punctuation">.</span><span class="token function">setTimestamp</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token class-name">Timestamp</span><span class="token punctuation">.</span><span class="token function">valueOf</span><span class="token punctuation">(</span>localDateTime<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span></pre></div><h2>使用建议</h2><ol start="1"><li><p class="ds-markdown-paragraph"><strong>新项目</strong>：强烈建议使用<code>LocalDateTime</code>，它是现代API且更安全易用</p></li><li><p class="ds-markdown-paragraph"><strong>旧系统维护</strong>：如果使用<code>Date</code>，注意其线程安全问题</p></li><li><p class="ds-markdown-paragraph"><strong>时区敏感场景</strong>：如果需要处理时区，考虑使用<code>ZonedDateTime</code>或<code>OffsetDateTime</code></p></li><li><p class="ds-markdown-paragraph"><strong>MySQL类型选择</strong>：</p><ul><li><p class="ds-markdown-paragraph">只需要日期：<code>DATE</code> + <code>LocalDate</code></p></li><li><p class="ds-markdown-paragraph">日期和时间：<code>DATETIME</code> + <code>LocalDateTime</code></p></li><li><p class="ds-markdown-paragraph">需要时区/自动更新：<code>TIMESTAMP</code> + 适当的Java类型</p></li></ul></li></ol><p class="ds-markdown-paragraph"><code>LocalDateTime</code>提供了更清晰、更安全的API来处理不含时区的日期时间信息，是<code>Date</code>类的现代化替代品。</p></div><div class="ds-flex" style="margin-top: 12px; height: 20px; margin-bottom: 20px; align-items: center; gap: 10px;"><div class="ds-flex _965abe9" style="align-items: center; gap: 16px;"><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.65169 12.9243C3.68173 13.1045 3.74181 13.2748 3.80189 13.445C3.87198 13.6052 3.96211 13.7654 4.06225 13.9156C4.16238 14.0658 4.27253 14.206 4.4027 14.3362C4.52286 14.4663 4.66306 14.5765 4.81326 14.6766C4.96346 14.7768 5.11366 14.8569 5.28389 14.927C5.44411 14.9971 5.61434 15.0571 5.79459 15.0872C5.97483 15.1272 6.14506 15.1373 6.3253 15.1373V16.9196C6.30739 16.9196 6.28949 16.9195 6.27159 16.9193C5.9991 16.9158 5.72659 16.8859 5.4541 16.8295C5.16371 16.7694 4.88334 16.6893 4.61298 16.5692C4.3326 16.459 4.08226 16.3188 3.83193 16.1586C3.59161 15.9884 3.3613 15.7981 3.15102 15.5878C2.94074 15.3776 2.7605 15.1473 2.59027 14.9069C2.43006 14.6566 2.28986 14.3962 2.17972 14.1259C2.06957 13.8455 1.97944 13.5651 1.91936 13.2747C1.86929 12.9843 1.83926 12.684 1.83926 12.3936V6.26532C1.83926 5.96492 1.86929 5.67456 1.91936 5.38417C1.97944 5.09378 2.06957 4.80338 2.17972 4.53302C2.28986 4.26265 2.43006 4.0023 2.59027 3.75197C2.7605 3.50163 2.94074 3.27132 3.15102 3.06104C3.3613 2.85076 3.59161 2.67052 3.83193 2.50029C4.08226 2.33006 4.3326 2.19987 4.61298 2.07971C4.88334 1.96956 5.16371 1.87943 5.4541 1.81935C5.74449 1.75927 6.03491 1.73926 6.3253 1.73926H12.3934C12.6838 1.73926 12.9842 1.75927 13.2746 1.81935C13.555 1.87943 13.8354 1.96956 14.1158 2.07971C14.3861 2.19987 14.6465 2.33006 14.8868 2.50029C15.1371 2.67052 15.3574 2.85076 15.5677 3.06104C15.778 3.27132 15.9582 3.50163 16.1284 3.75197C16.2887 4.0023 16.4288 4.26265 16.539 4.53302C16.6592 4.80338 16.7393 5.09378 16.7994 5.38417C16.8558 5.65722 16.8858 5.93024 16.8892 6.21161C16.8894 6.22948 16.8895 6.24739 16.8895 6.26532H15.1271C15.1271 6.08508 15.1071 5.90486 15.067 5.72462C15.037 5.55439 14.9869 5.38415 14.9168 5.21392C14.8467 5.04369 14.7566 4.88347 14.6665 4.73327C14.5664 4.58307 14.4462 4.45289 14.326 4.32271C14.1959 4.19254 14.0557 4.08239 13.9055 3.98226C13.7553 3.88212 13.6051 3.79202 13.4348 3.72193C13.2746 3.65184 13.1044 3.60174 12.9242 3.5717C12.7539 3.53165 12.5737 3.51163 12.3934 3.51163H6.3253C6.14506 3.51163 5.97483 3.53165 5.79459 3.5717C5.61434 3.60174 5.44411 3.65184 5.28389 3.72193C5.11366 3.79202 4.96346 3.88212 4.81326 3.98226C4.66306 4.08239 4.52286 4.19254 4.4027 4.32271C4.27253 4.45289 4.16238 4.58307 4.06225 4.73327C3.96211 4.88347 3.87198 5.04369 3.80189 5.21392C3.74181 5.38415 3.68173 5.55439 3.65169 5.72462C3.61164 5.90486 3.60164 6.08508 3.60164 6.26532V12.3936C3.60164 12.5638 3.61164 12.744 3.65169 12.9243Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.66972 21.6772C9.39936 21.567 9.13902 21.4268 8.8987 21.2566C8.64836 21.0964 8.42804 20.9061 8.21776 20.6959C8.00748 20.4856 7.81723 20.2553 7.65701 20.015C7.4968 19.7646 7.3566 19.5043 7.24646 19.2239C7.12629 18.9535 7.04621 18.6731 6.98613 18.3727C6.92605 18.0823 6.89601 17.792 6.89601 17.4915V11.3733C6.89601 11.0729 6.92605 10.7825 6.98613 10.4922C7.04621 10.1918 7.12629 9.91137 7.24646 9.64101C7.3566 9.36063 7.4968 9.10028 7.65701 8.85996C7.81723 8.60962 8.00748 8.37931 8.21776 8.16903C8.42804 7.95875 8.64836 7.76849 8.8987 7.60828C9.13902 7.43805 9.39936 7.29785 9.66972 7.1877C9.94009 7.07755 10.2205 6.98745 10.5108 6.92737C10.8012 6.86729 11.0916 6.83725 11.392 6.83725H17.4602C17.7506 6.83725 18.041 6.86729 18.3313 6.92737C18.6217 6.98745 18.9021 7.07755 19.1725 7.1877C19.4529 7.29785 19.7032 7.43805 19.9535 7.60828C20.1938 7.76849 20.4242 7.95875 20.6345 8.16903C20.8447 8.37931 21.025 8.60962 21.1952 8.85996C21.3554 9.10028 21.4956 9.36063 21.6058 9.64101C21.7159 9.91137 21.806 10.1918 21.8661 10.4922C21.9162 10.7825 21.9462 11.0729 21.9462 11.3733V17.4915C21.9462 17.792 21.9162 18.0823 21.8661 18.3727C21.806 18.6731 21.7159 18.9535 21.6058 19.2239C21.4956 19.5043 21.3554 19.7646 21.1952 20.015C21.025 20.2553 20.8447 20.4856 20.6345 20.6959C20.4242 20.9061 20.1938 21.0964 19.9535 21.2566C19.7032 21.4268 19.4529 21.567 19.1725 21.6772C18.9021 21.7973 18.6217 21.8774 18.3313 21.9375C18.041 21.9976 17.7506 22.0276 17.4602 22.0276H11.392C11.0916 22.0276 10.8012 21.9976 10.5108 21.9375C10.2205 21.8774 9.94009 21.7973 9.66972 21.6772ZM10.8613 8.6697C11.0316 8.63966 11.2118 8.61965 11.392 8.61965H17.4602C17.6404 8.61965 17.8107 8.63966 17.9909 8.6697C18.1611 8.70975 18.3314 8.75983 18.5016 8.82992C18.6618 8.90001 18.822 8.98012 18.9722 9.08026C19.1224 9.18039 19.2626 9.30055 19.3828 9.42071C19.513 9.55088 19.6231 9.69109 19.7232 9.84129C19.8234 9.99149 19.9035 10.1517 19.9736 10.3219C20.0437 10.4821 20.0937 10.6624 20.1338 10.8326C20.1638 11.0129 20.1838 11.1931 20.1838 11.3733V17.4915C20.1838 17.6718 20.1638 17.852 20.1338 18.0323C20.0937 18.2125 20.0437 18.3828 19.9736 18.543C19.9035 18.7132 19.8234 18.8734 19.7232 19.0236C19.6231 19.1738 19.513 19.314 19.3828 19.4342C19.2626 19.5643 19.1224 19.6845 18.9722 19.7846C18.822 19.8848 18.6618 19.9649 18.5016 20.035C18.3314 20.1051 18.1611 20.1551 17.9909 20.1952C17.8107 20.2252 17.6404 20.2452 17.4602 20.2452H11.392C11.2118 20.2452 11.0316 20.2252 10.8613 20.1952C10.6811 20.1551 10.5108 20.1051 10.3506 20.035C10.1804 19.9649 10.0202 19.8848 9.87 19.7846C9.72982 19.6845 9.58962 19.5643 9.45945 19.4342C9.33929 19.314 9.21913 19.1738 9.119 19.0236C9.01886 18.8734 8.93875 18.7132 8.86866 18.543C8.79857 18.3828 8.74847 18.2125 8.71843 18.0323C8.67838 17.852 8.65836 17.6718 8.65836 17.4915V11.3733C8.65836 11.1931 8.67838 11.0129 8.71843 10.8326C8.74847 10.6624 8.79857 10.4821 8.86866 10.3219C8.93875 10.1517 9.01886 9.99149 9.119 9.84129C9.21913 9.69109 9.33929 9.55088 9.45945 9.42071C9.58962 9.30055 9.72982 9.18039 9.87 9.08026C10.0202 8.98012 10.1804 8.90001 10.3506 8.82992C10.5108 8.75983 10.6811 8.70975 10.8613 8.6697Z" fill="currentColor"></path></svg></div></div><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px; opacity: 1;"><svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clippath id="clip1258_20811"><rect id="重新生成" width="20.000000" height="20.000000" fill="white" fill-opacity="0"></rect></clippath></defs><rect id="重新生成" width="20.000000" height="20.000000" fill="#FFFFFF" fill-opacity="0"></rect><g clip-path="url(#clip1258_20811)"><path id="path" d="M17.01 7.63L13.98 7.62C13.88 7.62 13.79 7.6 13.7 7.56C13.62 7.52 13.54 7.47 13.47 7.4C13.4 7.33 13.35 7.25 13.32 7.16C13.28 7.07 13.26 6.98 13.26 6.88C13.26 6.79 13.28 6.69 13.32 6.6C13.35 6.51 13.4 6.43 13.47 6.36C13.54 6.3 13.62 6.24 13.7 6.21C13.79 6.17 13.88 6.15 13.98 6.15L15.57 6.16C15.67 6.16 15.76 6.14 15.85 6.1C15.94 6.06 16.01 6.01 16.08 5.94C16.15 5.87 16.2 5.79 16.23 5.7C16.27 5.61 16.29 5.52 16.29 5.42L16.3 3.89C16.3 3.79 16.32 3.7 16.36 3.61C16.39 3.52 16.44 3.44 16.51 3.37C16.58 3.3 16.66 3.25 16.74 3.21C16.83 3.17 16.92 3.16 17.02 3.16C17.11 3.16 17.2 3.17 17.29 3.21C17.38 3.25 17.46 3.3 17.52 3.37C17.59 3.44 17.64 3.52 17.68 3.61C17.71 3.7 17.73 3.79 17.73 3.89L17.72 6.9C17.72 7 17.71 7.09 17.67 7.18C17.63 7.27 17.58 7.34 17.52 7.41C17.45 7.48 17.37 7.53 17.29 7.57C17.2 7.61 17.11 7.63 17.01 7.63Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M2.31 16.29L2.32 13.3C2.32 13.21 2.34 13.11 2.37 13.02C2.41 12.93 2.46 12.85 2.53 12.78C2.6 12.71 2.67 12.66 2.76 12.62C2.85 12.58 2.94 12.56 3.03 12.56L6.07 12.57C6.16 12.57 6.25 12.59 6.34 12.63C6.43 12.67 6.51 12.72 6.57 12.79C6.64 12.86 6.69 12.94 6.73 13.03C6.76 13.12 6.78 13.22 6.78 13.32C6.78 13.41 6.76 13.51 6.73 13.6C6.69 13.69 6.64 13.77 6.57 13.84C6.51 13.91 6.43 13.96 6.34 14C6.25 14.04 6.16 14.06 6.07 14.06L4.47 14.05C4.38 14.05 4.29 14.07 4.2 14.11C4.11 14.15 4.03 14.2 3.97 14.27C3.9 14.34 3.85 14.42 3.81 14.51C3.78 14.6 3.76 14.7 3.76 14.8L3.75 16.29C3.75 16.39 3.73 16.48 3.69 16.58C3.65 16.67 3.6 16.75 3.54 16.82C3.47 16.89 3.39 16.94 3.3 16.98C3.22 17.01 3.13 17.03 3.03 17.03C2.94 17.03 2.85 17.02 2.76 16.98C2.67 16.94 2.59 16.89 2.52 16.82C2.46 16.75 2.4 16.67 2.37 16.58C2.33 16.49 2.31 16.39 2.31 16.29Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M9.88 18.01C9.51 18.01 9.15 17.99 8.79 17.94C8.42 17.89 8.07 17.82 7.71 17.73C7.36 17.63 7.02 17.51 6.68 17.37C6.34 17.23 6.02 17.07 5.7 16.89C5.39 16.7 5.09 16.5 4.8 16.28C4.52 16.05 4.25 15.81 3.99 15.55C3.74 15.29 3.5 15.02 3.29 14.73C3.07 14.44 2.88 14.13 2.7 13.82L4.15 13.05C4.32 13.35 4.51 13.64 4.72 13.91C4.93 14.18 5.17 14.43 5.42 14.66C5.67 14.9 5.94 15.11 6.23 15.3C6.52 15.49 6.83 15.66 7.14 15.81C7.46 15.95 7.78 16.07 8.12 16.16C8.45 16.25 8.8 16.32 9.14 16.36C9.49 16.39 9.83 16.4 10.18 16.39C10.53 16.37 10.87 16.33 11.22 16.26C11.56 16.19 11.89 16.09 12.21 15.97C12.54 15.84 12.85 15.7 13.15 15.53C13.45 15.35 13.74 15.16 14.01 14.94C14.28 14.72 14.53 14.49 14.76 14.23C14.99 13.97 15.2 13.7 15.38 13.41C15.57 13.12 15.73 12.82 15.87 12.5C16 12.19 16.11 11.87 16.2 11.53C16.28 11.2 16.34 10.87 16.36 10.52C16.37 10.42 16.4 10.33 16.44 10.24C16.48 10.15 16.54 10.07 16.61 10C16.69 9.93 16.77 9.87 16.86 9.84C16.96 9.8 17.05 9.77 17.16 9.77C17.27 9.77 17.38 9.79 17.49 9.83C17.6 9.87 17.7 9.94 17.78 10.02C17.86 10.1 17.92 10.2 17.96 10.3C18 10.41 18.01 10.52 18 10.64C17.98 10.89 17.95 11.13 17.91 11.38C17.86 11.62 17.81 11.87 17.74 12.11C17.68 12.35 17.6 12.58 17.51 12.82C17.42 13.05 17.32 13.28 17.21 13.5C17.1 13.73 16.98 13.95 16.85 14.16C16.71 14.37 16.57 14.58 16.42 14.78C16.27 14.98 16.11 15.17 15.94 15.36C15.77 15.54 15.59 15.72 15.41 15.89C15.22 16.06 15.03 16.22 14.83 16.37C14.63 16.52 14.42 16.66 14.2 16.79C13.99 16.93 13.77 17.05 13.54 17.16C13.31 17.27 13.08 17.37 12.85 17.46C12.61 17.55 12.37 17.63 12.13 17.7C11.88 17.77 11.64 17.83 11.39 17.87C11.14 17.92 10.89 17.96 10.63 17.98C10.38 18 10.13 18.01 9.88 18.01Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M2.85 10.27C2.73 10.28 2.62 10.26 2.51 10.22C2.4 10.17 2.31 10.11 2.23 10.03C2.14 9.95 2.08 9.85 2.04 9.74C2 9.63 1.99 9.52 2 9.41C2.03 8.98 2.1 8.56 2.2 8.15C2.3 7.73 2.43 7.33 2.6 6.94C2.76 6.54 2.96 6.16 3.19 5.8C3.41 5.44 3.67 5.1 3.95 4.77C4.24 4.45 4.54 4.15 4.88 3.88C5.21 3.6 5.56 3.35 5.93 3.13C6.3 2.91 6.69 2.73 7.09 2.57C7.5 2.41 7.91 2.28 8.33 2.19C8.75 2.09 9.18 2.03 9.62 2.01C10.05 1.98 10.48 1.99 10.91 2.03C11.35 2.07 11.77 2.14 12.19 2.25C12.61 2.36 13.02 2.5 13.42 2.67C13.81 2.84 14.19 3.04 14.56 3.28C14.92 3.51 15.27 3.77 15.59 4.05C15.91 4.34 16.21 4.64 16.48 4.98C16.75 5.31 17 5.66 17.21 6.03L15.78 6.83C15.61 6.54 15.42 6.25 15.2 5.99C14.98 5.73 14.74 5.48 14.49 5.25C14.23 5.02 13.96 4.82 13.66 4.63C13.37 4.45 13.07 4.29 12.75 4.15C12.44 4.01 12.11 3.9 11.77 3.82C11.44 3.73 11.1 3.67 10.76 3.64C10.41 3.61 10.07 3.6 9.72 3.62C9.37 3.64 9.03 3.69 8.69 3.77C8.36 3.84 8.03 3.94 7.71 4.07C7.38 4.2 7.08 4.35 6.78 4.52C6.48 4.7 6.2 4.89 5.94 5.11C5.67 5.33 5.43 5.57 5.2 5.83C4.97 6.08 4.77 6.36 4.59 6.65C4.41 6.94 4.25 7.24 4.12 7.55C3.98 7.87 3.88 8.19 3.8 8.52C3.72 8.85 3.66 9.19 3.64 9.53C3.63 9.62 3.6 9.72 3.56 9.81C3.52 9.9 3.46 9.98 3.39 10.05C3.32 10.12 3.23 10.17 3.14 10.21C3.05 10.25 2.95 10.27 2.85 10.27Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path></g></svg></div></div><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M18.14 9.428l-.003.019L17.114 15a3.774 3.774 0 0 1-1.313 2.27 3.673 3.673 0 0 1-2.438.894H3.895c-.57 0-1.055-.205-1.458-.614a2.033 2.033 0 0 1-.604-1.48v-5.438c0-.578.201-1.071.604-1.48.402-.41.888-.614 1.458-.614h1.932l2.317-5.293c.24-.548.6-.948 1.083-1.2.599-.312 1.156-.278 1.672.103.782.577 1.172 1.355 1.172 2.334V6.997h3.994c.638-.007 1.167.235 1.588.726.421.492.584 1.06.488 1.705zm-2.076-2.6c.686-.007 1.262.256 1.714.785.453.529.63 1.146.527 1.84v.004l-.005.02v.003l-1.022 5.552a3.943 3.943 0 0 1-1.37 2.368 3.838 3.838 0 0 1-2.545.933H3.895a2.137 2.137 0 0 1-1.576-.663 2.203 2.203 0 0 1-.652-1.6v-5.437c0-.622.218-1.159.652-1.6.434-.44.962-.663 1.576-.663H5.72l2.273-5.192c.254-.58.64-1.01 1.159-1.282.32-.167.64-.246.953-.227.315.02.613.137.893.344.824.608 1.24 1.437 1.24 2.47v2.345h3.827zM9.741 3.063c-.247.128-.437.344-.57.647L6.708 9.34v7.683h6.655a2.59 2.59 0 0 0 1.72-.63c.498-.42.807-.955.926-1.601l1.02-5.544a.936.936 0 0 0-.225-.773.917.917 0 0 0-.739-.335h-4.557a.567.567 0 0 1-.562-.571V4.483c0-.591-.236-1.061-.708-1.41-.143-.105-.309-.109-.497-.01zm7.124 6.157a.766.766 0 0 0-.186-.636.753.753 0 0 0-.612-.276h-4.559a.734.734 0 0 1-.728-.74V4.483c0-.537-.21-.956-.64-1.273a.233.233 0 0 0-.137-.05.387.387 0 0 0-.185.053c-.208.108-.374.291-.495.566m0 0L6.874 9.375v7.479h6.489c.609 0 1.145-.197 1.614-.592.468-.396.757-.894.869-1.502l1.02-5.54M3.35 16.623c.*************.545.23h1.522V9.85H3.895a.732.732 0 0 0-.545.23.755.755 0 0 0-.226.553v5.437c0 .*************.553zm.545-6.941a.897.897 0 0 0-.663.278.924.924 0 0 0-.274.673v5.437c0 .263.091.487.274.673.183.186.404.28.663.28h1.688V9.681H3.895z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M18.137 9.447l.004-.02a2.044 2.044 0 0 0-.488-1.704c-.421-.491-.95-.733-1.588-.726h-2.282l1.584 1.142h.698a.917.917 0 0 1 .739.335.936.936 0 0 1 .225.773l-1.02 5.543a2.663 2.663 0 0 1-.926 1.602 2.59 2.59 0 0 1-1.72.63H6.708V9.34L9.171 3.71c.133-.303.323-.519.57-.647.188-.099.354-.095.497.01.472.349.708.819.708 1.41v.467l1.125.811V4.483c0-.98-.39-1.757-1.171-2.334-.517-.38-1.074-.415-1.673-.103-.482.252-.843.652-1.083 1.2L5.827 8.54H3.895c-.57 0-1.056.205-1.458.614a2.033 2.033 0 0 0-.604 1.48v5.437c0 .578.201 1.072.604 1.48.403.41.889.615 1.458.615h9.468c.918 0 1.731-.299 2.438-.895A3.774 3.774 0 0 0 17.114 15l1.023-5.553zm-14.242.235a.897.897 0 0 0-.663.278.924.924 0 0 0-.274.673v5.437c0 .263.091.487.274.673.183.186.404.28.663.28h1.688V9.681H3.895z" fill="currentColor"></path><path d="M10.946 7.568c0 .316.252.571.563.571h3.858l-1.584-1.142h-1.712V5.76l-1.125-.811v2.618z" fill="currentColor"></path></svg></div></div><div class="ds-icon-button" tabindex="0" style="--ds-icon-button-text-color: #909090; --ds-icon-button-size: 20px;"><div class="ds-icon" style="font-size: 20px; width: 20px; height: 20px;"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M18.304 10.543v.004a2.214 2.214 0 0 1-.526 1.84c-.452.528-1.028.792-1.714.784h-3.827v2.345c0 1.034-.416 1.863-1.24 2.471-.28.207-.578.324-.893.343-.314.02-.632-.06-.953-.226-.519-.271-.905-.702-1.159-1.282l-2.273-5.193H3.895a2.136 2.136 0 0 1-1.576-.663 2.203 2.203 0 0 1-.652-1.6V3.93c0-.623.218-1.16.652-1.6.434-.441.963-.663 1.576-.663h9.468c.957 0 1.807.311 2.544.933a3.943 3.943 0 0 1 1.37 2.368L18.3 10.52v.002l.004.02zm-1.19-5.544a3.774 3.774 0 0 0-1.313-2.27 3.673 3.673 0 0 0-2.438-.894H3.895c-.57 0-1.055.205-1.458.614a2.033 2.033 0 0 0-.604 1.48v5.437c0 .578.201 1.072.604 1.48.402.41.888.615 1.458.615h1.932l2.317 5.292c.24.549.6.949 1.083 1.2.599.313 1.156.279 1.672-.102.782-.577 1.172-1.355 1.172-2.335v-2.513h3.994c.638.007 1.167-.235 1.588-.727.421-.491.584-1.06.488-1.704l-.004-.02L17.114 5zM9.819 16.787c.**************.185.052a.233.233 0 0 0 .137-.05c.43-.316.64-.735.64-1.273v-3.084c0-.41.327-.74.729-.74h4.558a.752.752 0 0 0 .612-.277.766.766 0 0 0 .186-.635l-1.02-5.54a2.495 2.495 0 0 0-.869-1.502 2.426 2.426 0 0 0-1.613-.592H6.874v7.48l2.449 5.595m-2.615-5.56V2.978h6.655c.648 0 1.221.21 1.72.63.498.42.807.954.926 1.601l1.02 5.543a.936.936 0 0 1-.225.774.917.917 0 0 1-.739.335h-4.557a.567.567 0 0 0-.562.57v3.085c0 .592-.236 1.062-.708 1.41-.143.106-.309.11-.497.01-.247-.128-.437-.343-.57-.647l-2.463-5.628zM3.35 3.376a.754.754 0 0 0-.226.554v5.436c0 .*************.554.*************.545.23h1.522V3.146H3.895a.731.731 0 0 0-.545.23zm2.233-.398v7.34H3.895a.897.897 0 0 1-.663-.279.925.925 0 0 1-.274-.673V3.93c0-.263.091-.487.274-.673a.895.895 0 0 1 .663-.28h1.688zm4.235 13.809c-.208-.108-.375-.292-.495-.566l.495.566z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M15.801 2.73a3.774 3.774 0 0 1 1.313 2.269l1.023 5.554.004.019a2.044 2.044 0 0 1-.488 1.704c-.421.492-.95.734-1.588.727h-3.994v2.513c0 .98-.39 1.758-1.171 2.335-.517.38-1.074.415-1.673.102-.482-.251-.843-.651-1.083-1.2l-2.317-5.292H3.895c-.57 0-1.056-.205-1.458-.614a2.033 2.033 0 0 1-.604-1.48V3.93c0-.579.201-1.072.604-1.481.403-.41.889-.614 1.458-.614h9.468c.918 0 1.731.298 2.438.894zm-2.438.248c.648 0 1.221.21 1.72.63.498.42.807.954.926 1.601l1.02 5.543a.936.936 0 0 1-.225.774.917.917 0 0 1-.739.335h-4.557a.567.567 0 0 0-.562.57v3.085c0 .592-.236 1.062-.708 1.41-.143.106-.309.11-.497.01-.247-.128-.437-.343-.57-.647l-2.463-5.628V2.978h6.655zm-9.468 7.34a.897.897 0 0 1-.663-.279.925.925 0 0 1-.274-.673V3.93c0-.263.091-.487.274-.673a.895.895 0 0 1 .663-.28h1.688v7.341H3.895z" fill="currentColor"></path></svg></div></div></div><div style="flex: 1 1 0%;"></div></div></div></div><div class="_88681e8"><div class="_217e214"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.856 17.121a.979.979 0 0 1-.327-.06.839.839 0 0 1-.283-.177.739.739 0 0 1-.187-.255.724.724 0 0 1-.07-.303l-.02-1.609a4.663 4.663 0 0 1-1.446-.455 4.252 4.252 0 0 1-.637-.401c-.199-.146-.385-.31-.553-.492a4.442 4.442 0 0 1-.45-.577 4.303 4.303 0 0 1-.327-.637 3.823 3.823 0 0 1-.206-.686 3.729 3.729 0 0 1-.064-.704V6.478c0-.261.025-.516.077-.771a4.43 4.43 0 0 1 .244-.747 4.062 4.062 0 0 1 .932-1.28c.2-.183.418-.347.65-.493.23-.145.482-.267.739-.364a4.21 4.21 0 0 1 .81-.225c.27-.054.553-.078.835-.078H8.55c.103 0 .2.018.29.054a.7.7 0 0 1 .411.376.667.667 0 0 1-.161.766.736.736 0 0 1-.25.151.764.764 0 0 1-.29.055H5.573c-.186 0-.366.012-.54.049-.18.03-.353.079-.52.145-.167.061-.328.14-.482.237-.148.091-.29.2-.418.316a2.897 2.897 0 0 0-.347.388c-.097.14-.187.286-.257.444a2.473 2.473 0 0 0-.206.977v4.287c0 .17.013.333.051.503a2.549 2.549 0 0 0 .772 1.33 2.721 2.721 0 0 0 .913.559c.167.066.347.115.527.152.18.03.36.048.546.048a.904.904 0 0 1 .61.23.848.848 0 0 1 .194.262.84.84 0 0 1 .07.303l.007.99 1.915-1.293a2.877 2.877 0 0 1 1.64-.492h2.372c.186 0 .366-.018.54-.048.18-.03.353-.08.52-.146.168-.067.329-.146.483-.237.148-.091.29-.2.418-.316.128-.121.244-.249.347-.388a2.8 2.8 0 0 0 .257-.444 2.47 2.47 0 0 0 .206-.977V8.585a.646.646 0 0 1 .225-.492.679.679 0 0 1 .244-.152.814.814 0 0 1 .585 0c.09.03.174.085.244.152a.657.657 0 0 1 .225.492V10.8c0 .261-.032.516-.083.771a4.192 4.192 0 0 1-.245.74c-.109.244-.244.468-.398.687a3.735 3.735 0 0 1-.534.6c-.2.183-.418.347-.65.493a4.134 4.134 0 0 1-.738.364 4.7 4.7 0 0 1-.81.225c-.27.054-.553.079-.836.079h-1.877c-.604 0-1.144.164-1.633.491l-2.54 1.713a.913.913 0 0 1-.514.157z" fill="currentColor"></path><path d="M15.866 4.125h-4.174c-.41 0-.741.313-.741.7 0 .387.332.7.741.7h4.174c.41 0 .742-.313.742-.7 0-.387-.332-.7-.742-.7z" fill="currentColor"></path><path d="M14.537 2.932c0-.396-.34-.717-.759-.717s-.758.32-.758.717v3.786c0 .396.34.717.758.717.42 0 .76-.321.76-.717V2.932z" fill="currentColor"></path></svg><span>开启新对话</span></div></div><div class="_871cbca"><div class="aaff8b8f"><div class="_77cefa5"><div class="dd442025 _42b6996"><div class="_24fad49"><textarea id="chat-input" class="_27c9245" placeholder="给 DeepSeek 发送消息 " rows="2"></textarea><div class="b13855df">
</div></div><div class="ec4f5d61"><div role="button" class="ds-button ds-button--primary ds-button--filled ds-button--rect ds-button--m _3172d9f" tabindex="0" style="--ds-button-color: #fff; --button-text-color: #4c4c4c; --button-border-color: rgba(0, 0, 0, 0.12); --ds-button-hover-color: #E0E4ED;"><div class="ds-button__icon"><span style="transition: none; transform: rotate(0deg);"><div class="ds-icon" style="font-size: 19px; width: 19px; height: 19px; color: rgb(76, 76, 76);"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.656 17.344c-1.016-1.015-1.15-2.75-.313-4.925.325-.825.73-1.617 1.205-2.365L3.582 10l-.033-.054c-.5-.799-.91-1.596-1.206-2.365-.836-2.175-.703-3.91.313-4.926.56-.56 1.364-.86 2.335-.86 1.425 0 3.168.636 4.957 1.756l.053.034.053-.034c1.79-1.12 3.532-1.757 4.957-1.757.972 0 1.776.3 2.335.86 1.014 1.015 1.148 2.752.312 4.926a13.892 13.892 0 0 1-1.206 2.365l-.034.054.034.053c.5.8.91 1.596 1.205 2.365.837 2.175.704 3.911-.311 4.926-.56.56-1.364.861-2.335.861-1.425 0-3.168-.637-4.957-1.757L10 16.415l-.053.033c-1.79 1.12-3.532 1.757-4.957 1.757-.972 0-1.776-.3-2.335-.86zm13.631-4.399c-.187-.488-.429-.988-.71-1.492l-.075-.132-.092.12a22.075 22.075 0 0 1-3.968 3.968l-.12.093.132.074c1.308.734 2.559 1.162 3.556 1.162.563 0 1.006-.138 1.298-.43.3-.3.436-.774.428-1.346-.008-.575-.159-1.264-.449-2.017zm-6.345 1.65l.058.042.058-.042a19.881 19.881 0 0 0 4.551-4.537l.043-.058-.043-.058a20.123 20.123 0 0 0-2.093-2.458 19.732 19.732 0 0 0-2.458-2.08L10 5.364l-.058.042A19.883 19.883 0 0 0 5.39 9.942L5.348 10l.042.059c.631.874 1.332 1.695 2.094 2.457a19.74 19.74 0 0 0 2.458 2.08zm6.366-10.902c-.293-.293-.736-.431-1.298-.431-.998 0-2.248.429-3.556 1.163l-.132.074.12.092a21.938 21.938 0 0 1 3.968 3.968l.092.12.074-.132c.282-.504.524-1.004.711-1.492.29-.753.442-1.442.45-2.017.007-.572-.129-1.045-.429-1.345zM3.712 7.055c.202.514.44 1.013.712 1.493l.074.13.092-.119a21.94 21.94 0 0 1 3.968-3.968l.12-.092-.132-.074C7.238 3.69 5.987 3.262 4.99 3.262c-.563 0-1.006.138-1.298.43-.3.301-.436.774-.428 1.346.007.575.159 1.264.448 2.017zm0 5.89c-.29.753-.44 1.442-.448 2.017-.008.572.127 1.045.428 1.345.293.293.736.431 1.298.431.997 0 2.247-.428 3.556-1.162l.131-.074-.12-.093a21.94 21.94 0 0 1-3.967-3.968l-.093-.12-.074.132a11.712 11.712 0 0 0-.71 1.492z" fill="currentColor" stroke="currentColor" stroke-width=".1"></path><path d="M10.706 11.704A1.843 1.843 0 0 1 8.155 10a1.845 1.845 0 1 1 2.551 1.704z" fill="currentColor" stroke="currentColor" stroke-width=".2"></path></svg></div></span></div><span class="ad0c98fd">深度思考 (R1)</span></div><div role="button" class="ds-button ds-button--primary ds-button--filled ds-button--rect ds-button--m _3172d9f" tabindex="0" style="--ds-button-color: #fff; --button-text-color: #4c4c4c; --button-border-color: rgba(0, 0, 0, 0.12); --ds-button-hover-color: #E0E4ED;"><div class="ds-button__icon"><span style="transition: none; transform: rotate(0deg);"><div class="ds-icon" style="font-size: 17px; width: 17px; height: 17px; color: rgb(76, 76, 76);"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="10" cy="10" r="9" stroke="currentColor" stroke-width="1.8"></circle><path d="M10 1c1.657 0 3 4.03 3 9s-1.343 9-3 9M10 19c-1.657 0-3-4.03-3-9s1.343-9 3-9M1 10h18" stroke="currentColor" stroke-width="1.8"></path></svg></div></span></div><span class="ad0c98fd">联网搜索</span></div><div class="bf38813a"><div class="f02f0e25"><div class="ds-icon" style="font-size: 23px; width: 23px; height: 23px;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 20" fill="none"><path d="M7 20c-1.856-.002-3.635-.7-4.947-1.94C.74 16.819.003 15.137 0 13.383V4.828a4.536 4.536 0 0 1 .365-1.843 4.75 4.75 0 0 1 1.087-1.567A5.065 5.065 0 0 1 3.096.368a5.293 5.293 0 0 1 3.888 0c.616.244 1.174.6 1.643 1.05.469.45.839.982 1.088 1.567.25.586.373 1.212.364 1.843v8.555a2.837 2.837 0 0 1-.92 2.027A3.174 3.174 0 0 1 7 16.245c-.807 0-1.582-.3-2.158-.835a2.837 2.837 0 0 1-.92-2.027v-6.22a1.119 1.119 0 1 1 2.237 0v6.22a.777.777 0 0 0 .256.547.868.868 0 0 0 .585.224c.219 0 .429-.08.586-.224a.777.777 0 0 0 .256-.546V4.828A2.522 2.522 0 0 0 7.643 3.8a2.64 2.64 0 0 0-.604-.876 2.816 2.816 0 0 0-.915-.587 2.943 2.943 0 0 0-2.168 0 2.816 2.816 0 0 0-.916.587 2.64 2.64 0 0 0-.604.876 2.522 2.522 0 0 0-.198 1.028v8.555c0 1.194.501 2.339 1.394 3.183A4.906 4.906 0 0 0 7 17.885a4.906 4.906 0 0 0 3.367-1.319 4.382 4.382 0 0 0 1.395-3.183v-6.22a1.119 1.119 0 0 1 2.237 0v6.22c-.002 1.754-.74 3.436-2.052 4.677C10.635 19.3 8.856 19.998 7 20z" fill="currentColor"></path></svg></div></div><input type="file" multiple="" accept=".epub,.mobi,.azw3,.pdf,.png,.jpg,.jpeg,.svg,.svgz,.bmp,.gif,.webp,.ico,.xbm,.dib,.pjp,.tif,.pjpeg,.avif,.apng,.tiff,.jfif,.txt,.md,.csv,.tsv,.html,.json,.log,.dot,.go,.h,.c,.cpp,.cxx,.cc,.cs,.java,.js,.css,.jsp,.php,.py,.py3,.asp,.yaml,.yml,.ini,.conf,.ts,.tsx,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.abap,.asc,.ash,.ampl,.mod,.g4,.apib,.apl,.dyalog,.asax,.ascx,.ashx,.asmx,.aspx,.axd,.dats,.hats,.sats,.as,.adb,.ada,.ads,.agda,.als,.apacheconf,.vhost,.cls,.applescript,.scpt,.arc,.ino,.asciidoc,.adoc,.aj,.asm,.a51,.inc,.nasm,.aug,.ahk,.ahkl,.au3,.awk,.auk,.gawk,.mawk,.nawk,.bat,.cmd,.befunge,.bison,.bb,.decls,.bmx,.bsv,.boo,.b,.bf,.brs,.bro,.cats,.idc,.w,.cake,.cshtml,.csx,.c++,.cp,.h++,.hh,.hpp,.hxx,.inl,.ipp,.tcc,.tpp,.c-objdump,.chs,.clp,.cmake,.in,.cob,.cbl,.ccp,.cobol,.cpy,.capnp,.mss,.ceylon,.chpl,.ch,.ck,.cirru,.clw,.icl,.dcl,.click,.clj,.boot,.cl2,.cljc,.cljs,.hl,.cljscm,.cljx,.hic,.coffee,._coffee,.cjsx,.cson,.iced,.cfm,.cfml,.cfc,.lisp,.asd,.cl,.l,.lsp,.ny,.podsl,.sexp,.cps,.coq,.v,.cppobjdump,.c++-objdump,.c++objdump,.cpp-objdump,.cxx-objdump,.creole,.cr,.feature,.cu,.cuh,.cy,.pyx,.pxd,.pxi,.d,.di,.d-objdump,.com,.dm,.zone,.arpa,.darcspatch,.dpatch,.dart,.diff,.patch,.dockerfile,.djs,.dylan,.dyl,.intr,.lid,.E,.ecl,.eclxml,.sch,.brd,.epj,.e,.ex,.exs,.elm,.el,.emacs,.desktop,.em,.emberscript,.erl,.es,.escript,.hrl,.xrl,.yrl,.fs,.fsi,.fsx,.fx,.flux,.f90,.f,.f03,.f08,.f77,.f95,.for,.fpp,.factor,.fy,.fancypack,.fan,.fth,.4th,.forth,.fr,.frt,.ftl,.g,.gco,.gcode,.gms,.gap,.gd,.gi,.tst,.s,.ms,.glsl,.fp,.frag,.frg,.fsh,.fshader,.geo,.geom,.glslv,.gshader,.shader,.vert,.vrx,.vsh,.vshader,.gml,.kid,.ebuild,.eclass,.po,.pot,.glf,.gp,.gnu,.gnuplot,.plot,.plt,.golo,.gs,.gst,.gsx,.vark,.grace,.gradle,.gf,.graphql,.gv,.man,.1in,.1m,.1x,.3in,.3m,.3qt,.3x,.me,.n,.rno,.roff,.groovy,.grt,.gtpl,.gvy,.gsp,.hcl,.tf,.hlsl,.fxh,.hlsli,.htm,.st,.xht,.xhtml,.mustache,.jinja,.eex,.erb,.deface,.phtml,.http,.haml,.handlebars,.hbs,.hb,.hs,.hsc,.hx,.hxsl,.hy,.pro,.dlm,.ipf,.cfg,.prefs,.properties,.irclog,.weechatlog,.idr,.lidr,.ni,.i7x,.iss,.io,.ik,.thy,.ijs,.flex,.jflex,.geojson,.lock,.topojson,.json5,.jsonld,.jq,.jsx,.jade,.j,._js,.bones,.es6,.jake,.jsb,.jscad,.jsfl,.jsm,.jss,.njs,.pac,.sjs,.ssjs,.sublime-build,.sublime-commands,.sublime-completions,.sublime-keymap,.sublime-macro,.sublime-menu,.sublime-mousemap,.sublime-project,.sublime-settings,.sublime-theme,.sublime-workspace,.sublime_metrics,.sublime_session,.xsjs,.xsjslib,.jl,.ipynb,.krl,.kicad_pcb,.kit,.kt,.ktm,.kts,.lfe,.ll,.lol,.lsl,.lslp,.lvproj,.lasso,.las,.lasso8,.lasso9,.ldml,.latte,.lean,.hlean,.less,.lex,.ly,.ily,.m,.ld,.lds,.liquid,.lagda,.litcoffee,.lhs,.ls,._ls,.xm,.x,.xi,.lgt,.logtalk,.lookml,.lua,.fcgi,.nse,.pd_lua,.rbxs,.wlua,.mumps,.m4,.mcr,.mtml,.muf,.mak,.mk,.mkfile,.mako,.mao,.markdown,.mkd,.mkdn,.mkdown,.ron,.mask,.mathematica,.cdf,.ma,.map,.mt,.nb,.nbp,.wl,.wlt,.matlab,.maxpat,.maxhelp,.maxproj,.mxt,.pat,.mediawiki,.wiki,.moo,.metal,.minid,.druby,.duby,.mir,.mirah,.mo,.mms,.mmk,.monkey,.moon,.myt,.ncl,.nl,.nsi,.nsh,.axs,.axi,.nlogo,.nginxconf,.nim,.nimrod,.ninja,.nit,.nix,.nu,.numpy,.numpyw,.numsc,.ml,.eliom,.eliomi,.ml4,.mli,.mll,.mly,.objdump,.mm,.sj,.omgrofl,.opa,.opal,.opencl,.p,.scad,.org,.ox,.oxh,.oxo,.oxygene,.oz,.pwn,.aw,.ctp,.php3,.php4,.php5,.php6,.php7,.php8,.phps,.phpt,.pls,.pck,.pkb,.pks,.plb,.plsql,.sql,.pov,.pan,.psc,.parrot,.pasm,.pir,.pas,.dfm,.dpr,.lpr,.pp,.pl,.al,.cgi,.perl,.ph,.plx,.pm,.pod,.psgi,.t,.6pl,.6pm,.nqp,.p6,.p6l,.p6m,.pl6,.pm6,.pkl,.pig,.pike,.pmod,.pogo,.pony,.ps,.eps,.ps1,.psd1,.psm1,.pde,.prolog,.yap,.spin,.proto,.pub,.pd,.pb,.pbi,.purs,.bzl,.gyp,.lmi,.pyde,.pyi,.pyp,.pyt,.pyw,.rpy,.tac,.wsgi,.xpy,.pytb,.qml,.qbs,.pri,.r,.rd,.rsx,.raml,.rdoc,.rbbas,.rbfrm,.rbmnu,.rbres,.rbtbar,.rbuistate,.rhtml,.rmd,.rkt,.rktd,.rktl,.scrbl,.rl,.raw,.reb,.r2,.r3,.rebol,.red,.reds,.cw,.rs,.rsh,.robot,.rg,.rb,.builder,.gemspec,.god,.irbrc,.jbuilder,.mspec,.pluginspec,.podspec,.rabl,.rake,.rbuild,.rbw,.rbx,.ru,.ruby,.thor,.watchr,.sas,.scss,.smt2,.smt,.sparql,.rq,.sqf,.hqf,.cql,.ddl,.prc,.tab,.udf,.viw,.db2,.ston,.sage,.sagews,.sls,.sass,.scala,.sbt,.sc,.scaml,.scm,.sld,.sps,.ss,.sci,.sce,.self,.sh,.bash,.bats,.command,.ksh,.tmux,.tool,.zsh,.sh-session,.shen,.sl,.slim,.smali,.tpl,.sp,.sma,.nut,.stan,.ML,.fun,.sig,.sml,.do,.ado,.doh,.ihlp,.mata,.matah,.sthlp,.styl,.scd,.swift,.sv,.svh,.vh,.toml,.txl,.tcl,.adp,.tm,.tcsh,.csh,.tex,.aux,.bbx,.bib,.cbx,.dtx,.ins,.lbx,.ltx,.mkii,.mkiv,.mkvi,.sty,.toc,.tea,.no,.textile,.thrift,.tu,.ttl,.twig,.upc,.anim,.asset,.mat,.meta,.prefab,.unity,.uno,.uc,.ur,.urs,.vcl,.vhdl,.vhd,.vhf,.vhi,.vho,.vhs,.vht,.vhw,.vala,.vapi,.veo,.vim,.vb,.bas,.frm,.frx,.vba,.vbhtml,.vbs,.volt,.vue,.owl,.webidl,.x10,.xc,.xml,.ant,.axml,.ccxml,.clixml,.cproject,.csl,.csproj,.ct,.dita,.ditamap,.ditaval,.config,.dotsettings,.filters,.fsproj,.fxml,.glade,.grxml,.iml,.ivy,.jelly,.jsproj,.kml,.launch,.mdpolicy,.mxml,.nproj,.nuspec,.odd,.osm,.plist,.props,.ps1xml,.psc1,.pt,.rdf,.rss,.scxml,.srdf,.storyboard,.stTheme,.sublime-snippet,.targets,.tmCommand,.tml,.tmLanguage,.tmPreferences,.tmSnippet,.tmTheme,.ui,.urdf,.ux,.vbproj,.vcxproj,.vssettings,.vxml,.wsdl,.wsf,.wxi,.wxl,.wxs,.x3d,.xacro,.xaml,.xib,.xlf,.xliff,.xmi,.dist,.xproj,.xsd,.xul,.zcml,.xsp-config,.metadata,.xpl,.xproc,.xquery,.xq,.xql,.xqm,.xqy,.xs,.xslt,.xsl,.xojo_code,.xojo_menu,.xojo_report,.xojo_script,.xojo_toolbar,.xojo_window,.xtend,.reek,.rviz,.sublime-syntax,.syntax,.yaml-tmlanguage,.yang,.y,.yacc,.yy,.zep,.zimpl,.zmpl,.zpl,.ec,.eh,.edn,.fish,.mu,.nc,.ooc,.rst,.rest,.wisp,.prg,.prw,.gitignore,.gitkeep,.gitmodules,.example,.avifs,.blp,.bufr,.bw,.cur,.dcx,.dds,.emf,.fit,.fits,.flc,.fli,.ftc,.ftu,.gbr,.grib,.h5,.hdf,.hif,.icb,.icns,.iim,.im,.j2c,.j2k,.jp2,.jpc,.jpe,.jpf,.jpx,.mpeg,.mpg,.msp,.pbm,.pcd,.pcx,.pfm,.pgm,.pnm,.ppm,.psd,.pxr,.qoi,.ras,.rgb,.rgba,.sgi,.tga,.vda,.vst,.wmf,.xpm" style="display: none;"><div role="button" aria-disabled="true" class="_7436101 bcc55ca1"><div class="_6f28693"><div class="ds-icon" style="font-size: 16px; width: 16px; height: 16px;"><svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 16c-.595 0-1.077-.462-1.077-1.032V1.032C5.923.462 6.405 0 7 0s1.077.462 1.077 1.032v13.936C8.077 15.538 7.595 16 7 16z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M.315 7.44a1.002 1.002 0 0 1 0-1.46L6.238.302a1.11 1.11 0 0 1 1.523 0c.421.403.421 1.057 0 1.46L1.838 7.44a1.11 1.11 0 0 1-1.523 0z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M13.685 7.44a1.11 1.11 0 0 1-1.523 0L6.238 1.762a1.002 1.002 0 0 1 0-1.46 1.11 1.11 0 0 1 1.523 0l5.924 5.678c.42.403.42 1.056 0 1.46z" fill="currentColor"></path></svg></div></div></div></div></div></div></div></div><div class="_0fcaa63">内容由 AI 生成，请仔细甄别</div></div></div></div></div></div></div></div></div></div></div><div class="ds-notification-container ds-theme" style="--ds-rgb-hover: 0 0 0 / 4%; font-size: var(--ds-font-size-m); line-height: var(--ds-line-height-m);"></div><iframe id="intercom-frame" style="position: absolute !important; opacity: 0 !important; width: 1px !important; height: 1px !important; top: 0 !important; left: 0 !important; border: none !important; display: block !important; z-index: -1 !important; pointer-events: none;" aria-hidden="true" tabindex="-1" title="Intercom" src="./Java中Date与LocalDate区别及MySQL插入方式 - DeepSeek_files/saved_resource.html"></iframe><div class="intercom-lightweight-app"><style id="intercom-lightweight-app-style" type="text/css">
  @keyframes intercom-lightweight-app-launcher {
    from {
      opacity: 0;
      transform: scale(0.5);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes intercom-lightweight-app-gradient {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes intercom-lightweight-app-messenger {
    0% {
      opacity: 0;
      transform: scale(0);
    }
    40% {
      opacity: 1;
    }
    100% {
      transform: scale(1);
    }
  }

  .intercom-lightweight-app {
    position: fixed;
    z-index: 2147483001;
    width: 0;
    height: 0;
    font-family: intercom-font, "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
  }

  .intercom-lightweight-app-gradient {
    position: fixed;
    z-index: 2147483002;
    width: 500px;
    height: 500px;
    bottom: 0;
    right: 0;
    pointer-events: none;
    background: radial-gradient(
      ellipse at bottom right,
      rgba(29, 39, 54, 0.16) 0%,
      rgba(29, 39, 54, 0) 72%);
    animation: intercom-lightweight-app-gradient 200ms ease-out;
  }

  .intercom-lightweight-app-launcher {
    position: fixed;
    z-index: 2147483003;
    padding: 0 !important;
    margin: 0 !important;
    border: none;
    bottom: 20px;
    right: 20px;
    max-width: 48px;
    width: 48px;
    max-height: 48px;
    height: 48px;
    border-radius: 50%;
    background: #0f0f0f;
    cursor: pointer;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06), 0 2px 32px 0 rgba(0, 0, 0, 0.16);
    transition: transform 167ms cubic-bezier(0.33, 0.00, 0.00, 1.00);
    box-sizing: content-box;
  }


  .intercom-lightweight-app-launcher:hover {
    transition: transform 250ms cubic-bezier(0.33, 0.00, 0.00, 1.00);
    transform: scale(1.1)
  }

  .intercom-lightweight-app-launcher:active {
    transform: scale(0.85);
    transition: transform 134ms cubic-bezier(0.45, 0, 0.2, 1);
  }


  .intercom-lightweight-app-launcher:focus {
    outline: none;

    
  }

  .intercom-lightweight-app-launcher-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 48px;
    height: 48px;
    transition: transform 100ms linear, opacity 80ms linear;
  }

  .intercom-lightweight-app-launcher-icon-open {
    
        opacity: 1;
        transform: rotate(0deg) scale(1);
      
  }

  .intercom-lightweight-app-launcher-icon-open svg {
    width: 24px;
    height: 24px;
  }

  .intercom-lightweight-app-launcher-icon-open svg path {
    fill: rgb(255, 255, 255);
  }

  .intercom-lightweight-app-launcher-icon-self-serve {
    
        opacity: 1;
        transform: rotate(0deg) scale(1);
      
  }

  .intercom-lightweight-app-launcher-icon-self-serve svg {
    height: 44px;
  }

  .intercom-lightweight-app-launcher-icon-self-serve svg path {
    fill: rgb(255, 255, 255);
  }

  .intercom-lightweight-app-launcher-custom-icon-open {
    max-height: 24px;
    max-width: 24px;

    
        opacity: 1;
        transform: rotate(0deg) scale(1);
      
  }

  .intercom-lightweight-app-launcher-icon-minimize {
    
        opacity: 0;
        transform: rotate(-60deg) scale(0);
      
  }

  .intercom-lightweight-app-launcher-icon-minimize svg path {
    fill: rgb(255, 255, 255);
  }

  .intercom-lightweight-app-messenger {
    position: fixed;
    z-index: 2147483003;
    overflow: hidden;
    background-color: #ffffff;
    animation: intercom-lightweight-app-messenger 250ms cubic-bezier(0, 1, 1, 1);
    transform-origin: bottom right;

    
        width: 400px;
        height: calc(100% - 40px);
        max-height: 704px;
        min-height: 250px;
        right: 20px;
        bottom: 20px;
        box-shadow: 0 5px 40px rgba(0,0,0,0.16);
      

    border-radius: 16px;
  }

  .intercom-lightweight-app-messenger-header {
    height: 64px;
    border-bottom: none;
    background: #ffffff;
  }

  .intercom-lightweight-app-messenger-footer{
    position:absolute;
    bottom:0;
    width: 100%;
    height: 80px;
    background: #ffffff;
    font-size: 14px;
    line-height: 21px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.05);
  }

  @media print {
    .intercom-lightweight-app {
      display: none;
    }
  }
</style></div><kimi-web-extension data-wxt-shadow-root="" style="z-index: 2147483647; overflow: visible; position: relative; width: 0px; height: 0px; display: block;"><template shadowrootmode="open">
<!-- saved from url=(0071)https://chat.deepseek.com/a/chat/s/41859549-b26f-4db7-9cf8-db661db6a4ea -->
<html style="position: absolute; top: 0px; left: 0px;"><head><style>:host{--z-index: 2147483647;--animation-duration: .2s;--color-black: #000;--color-white: #fff;--color-border: rgba(0, 0, 0, .2);--color-text-primary: rgba(0, 0, 0, .85);--color-text-secondary: rgba(0, 0, 0, .6);--color-text-tertiary: rgba(0, 0, 0, .3);--color-fills-primary: #e8e8e8;--color-fills-secondary: #f2f2f2;--color-fills-tertiary: #f5f5f5;--color-accents-blue: #007aff;--color-fill-dark-5: rgba(0, 0, 0, .05)}*{box-sizing:border-box}html,body{margin:0;visibility:visible;font-family:-apple-system,blinkmacsystemfont,Helvetica Neue,helvetica,segoe ui,arial,roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}textarea{font-family:-apple-system,blinkmacsystemfont,Helvetica Neue,helvetica,segoe ui,arial,roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif}::-webkit-scrollbar{width:4px;height:4px}::-webkit-scrollbar-track{background-color:transparent}::-webkit-scrollbar-thumb{border-radius:2px;background-color:var(--color-text-tertiary)}@media print{#kimi-web-extension{display:none}}._modal_apzh3_1{display:flex;flex-flow:column;overflow:hidden;backdrop-filter:blur(40px);-webkit-backdrop-filter:blur(40px)}._explore_apzh3_10{position:fixed;z-index:var(--z-index);border-radius:12px;box-shadow:0 0 1px #0009,0 0 2px #0000000d,0 38px 90px #00000040;background:linear-gradient(0deg,#ffffffb8,#ffffffb8),linear-gradient(0deg,#f6f6f666,#f6f6f666),#26262633}._quickinput_apzh3_29{margin:25vh auto 0;width:30vw;min-width:560px;max-width:640px;border-radius:12px;box-shadow:0 0 2px #0000000d,0 38px 90px #00000040;background:linear-gradient(0deg,#ffffffd6,#ffffffd6),linear-gradient(0deg,#f6f6f666,#f6f6f666),#26262633}._chat_apzh3_52{margin:25vh auto 0;width:50vw;min-width:560px;max-width:880px;height:55vh;min-height:300px;max-height:1200px;border-radius:16px;box-shadow:0 0 2px #0000000d,0 38px 90px #00000040;background:var( --Background-Gradient---84, linear-gradient( 0deg, rgba(255, 255, 255, .84) 0%, rgba(255, 255, 255, .84) 100% ), linear-gradient( 0deg, rgba(246, 246, 246, .4) 0%, rgba(246, 246, 246, .4) 100% ), rgba(38, 38, 38, .2) )}._enter_m5xms_1{height:32px;cursor:pointer;margin-left:auto;display:flex;align-items:center;gap:8px;border-radius:6px;color:var(--color-text-secondary);transition:color var(--animation-duration),background-color var(--animation-duration)}._hover_m5xms_17:hover{background-color:var(--color-fill-dark-5)}._disabled_m5xms_23{color:var(--color-text-tertiary)}._disabled_m5xms_23:hover{background-color:transparent}._stop_ch54z_1{cursor:pointer;width:32px;height:32px;border-radius:6px;display:flex;align-items:center;justify-content:center;transition:background-color var(--animation-duration)}._stop_ch54z_1:hover{background-color:var(--color-fill-dark-5)}._center_ch54z_18{width:14px;height:14px;border-radius:2px;background-color:var(--color-text-secondary)}._input_1hgaj_1{display:flex;align-items:flex-end;flex-flow:wrap;gap:8px;border-radius:8px;transition:border-radius var(--animation-duration)}._background_1hgaj_10{background-color:var(--color-fill-dark-5)}._textarea_1hgaj_14{flex:1;border:none;outline:none;font-size:16px;line-height:24px;padding:12px 16px;color:var(--color-text-primary);caret-color:var(--color-accents-blue);box-shadow:none!important;background:transparent!important}._textarea_1hgaj_14::placeholder{color:var(--color-text-tertiary)}._enter_1hgaj_31{margin:0 12px 8px 0}._list_1mdhs_1{flex:1;overflow:auto;display:flex;flex-flow:column-reverse}._question_11rv8_1{margin-bottom:12px;display:flex;align-items:center;gap:16px}._content_11rv8_8{flex:1;color:var(--color-text-primary);font-size:18px;text-align:left;font-weight:600;line-height:30px}._segment_11wth_1{display:flex;flex-flow:column}._segment_11wth_1:hover .chat-segment-actions{opacity:1}._copy_ol4z9_1{cursor:pointer;color:var(--color-text-secondary);transition:color var(--animation-duration)}._copy_ol4z9_1:hover{color:var(--color-text-primary)}._copied_ol4z9_11{color:var(--color-accents-blue)!important}._actions_1w0dp_1{height:24px;display:flex;gap:12px;opacity:0;transition:opacity var(--animation-duration)}._actions_1w0dp_1 span{cursor:pointer;color:var(--color-text-secondary);transition:color var(--animation-duration)}._actions_1w0dp_1 span:hover{color:var(--color-text-primary)}._actions_1w0dp_1 ._liked_1w0dp_19,._actions_1w0dp_1 ._disliked_1w0dp_20,._actions_1w0dp_1 ._liked_1w0dp_19:hover,._actions_1w0dp_1 ._disliked_1w0dp_20:hover{color:var(--color-accents-blue)}._canceled_16w5c_1{color:var(--color-text-tertiary);text-align:left;font-size:12px;font-style:normal;font-weight:400;line-height:27px;letter-spacing:.25px}._markdown_hsyph_1{--bg: rgba(255, 255, 255, .8);color:var(--color-text-primary);font-size:16px;font-style:normal;font-weight:400;text-align:left;line-height:27px}._markdown_hsyph_1 h1,._markdown_hsyph_1 h2,._markdown_hsyph_1 h3,._markdown_hsyph_1 h4,._markdown_hsyph_1 h5,._markdown_hsyph_1 h6,._markdown_hsyph_1 p,._markdown_hsyph_1 ul,._markdown_hsyph_1 ol{margin:12px 0}._markdown_hsyph_1 p:first-of-type,._markdown_hsyph_1 ul:first-of-type,._markdown_hsyph_1 ol:first-of-type{margin-top:0}._markdown_hsyph_1 p:last-of-type,._markdown_hsyph_1 ul:last-of-type,._markdown_hsyph_1 ol:last-of-type{margin-bottom:0}._markdown_hsyph_1 li{margin:6px 0}._markdown_hsyph_1 p,._markdown_hsyph_1 code{max-width:100%;overflow:auto;white-space:pre-wrap}._markdown_hsyph_1 pre>div{border-radius:8px!important}._markdown_hsyph_1 var{font-style:normal;margin:0 2px;padding:2px 4px;border-radius:4px;background-color:var(--color-fill-dark-5);font-family:Fira Code,Fira Mono,Menlo,Consolas,DejaVu Sans Mono,monospace}._markdown_hsyph_1 table{width:max-content;max-width:100%;overflow:auto;display:block;margin:12px 0;border-spacing:0;border-collapse:collapse;border-radius:8px;background-color:var(--bg)}._markdown_hsyph_1 table th,._markdown_hsyph_1 table td{padding:8px 12px;border:1px solid var(--color-fill-dark-5)}._content_zpgxp_1{overflow:hidden;font-size:16px;line-height:27px}._right_zpgxp_7{max-width:70%;padding:10px 12px;margin-left:auto;color:var(--color-white);border-radius:12px;background:var(--color-accents-blue)}._full_zpgxp_16{max-width:100%}._files_1mhs5_1{margin-top:12px;display:flex;flex-flow:wrap;justify-content:flex-end;gap:8px}._file_1mhs5_1{width:84px;height:84px;object-fit:cover;border-radius:4px}._header_1oq8b_1{margin:24px 0 12px;display:flex;align-items:center;gap:8px}._name_1oq8b_8{color:var(--color-text-secondary);font-size:16px;font-weight:600;line-height:24px}._divider_1sh7y_1{height:1px;flex-shrink:0;background-color:var(--color-fill-dark-5)}._container_4ugsa_1{position:relative}._snippets_4ugsa_5 ._list_4ugsa_6{margin:0 4px;padding-left:12px;padding-right:12px}._snippets_4ugsa_5 ._overflow_4ugsa_12{padding-right:8px}._popup_4ugsa_17{width:100%;position:absolute;left:0;bottom:calc(100% + 8px);border-radius:12px;background-color:var(--color-white);border:1px solid var(--color-fill-dark-5)}._popup_4ugsa_17 ._list_4ugsa_6{margin:4px}._popup_4ugsa_17 ._overflow_4ugsa_12{margin-right:2px;padding-right:2px}._list_4ugsa_6{overflow:auto;max-height:240px}._item_4ugsa_42{cursor:pointer;height:48px;padding:0 12px;border-radius:8px;display:flex;align-items:center;gap:8px;color:var(--color-text-secondary);transition:all var(--animation-duration) ease-in-out}._emoji_4ugsa_55{width:24px;height:24px;font-size:18px}._name_4ugsa_61{flex:1;text-align:left;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:16px;font-style:normal;font-weight:500;line-height:24px;letter-spacing:.25px}._name_4ugsa_61 em{font-style:normal;color:var(--color-accents-blue)}._selected_4ugsa_79{color:var(--color-text-primary);background-color:var(--color-fill-dark-5)}._file_1a4da_1{flex-shrink:0;width:64px;height:64px;position:relative;border-radius:4px}._file_1a4da_1:hover ._close_1a4da_10{opacity:1;transform:scale(1)}._image_1a4da_17{width:100%;height:100%;object-fit:cover;border-radius:4px;-webkit-user-select:none;user-select:none;-webkit-user-drag:none}._mask_1a4da_26{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:4px;background-color:#0006;display:flex;align-items:center;justify-content:center}._error_1a4da_39{cursor:pointer}@keyframes _rotate_1a4da_1{0%{transform:rotate(0)}to{transform:rotate(360deg)}}._loading_1a4da_53{animation:_rotate_1a4da_1 1.2s linear infinite}._close_1a4da_10{cursor:pointer;position:absolute;top:-6.5px;right:-6.5px;opacity:0;transform:scale(0);transition:all var(--animation-duration)}._container_1r2u4_1{position:relative}._background_1r2u4_5{border-radius:8px 8px 0 0;background-color:var(--color-white);border:1px solid var(--color-fill-dark-5);border-bottom:none}._files_1r2u4_12{padding:8px;overflow-x:auto;overflow-y:hidden;display:flex;align-items:center;gap:8px}._limit_1r2u4_22{position:absolute;top:0;right:0;bottom:0;left:0;margin:auto;width:fit-content;height:fit-content;display:flex;align-items:center;justify-content:center;gap:6px;padding:16px;border-radius:12px;-webkit-backdrop-filter:blur(40px);backdrop-filter:blur(40px);background-color:var(--color-text-tertiary);color:var(--color-white);font-size:16px;line-height:24px}@keyframes _spread_866n9_1{0%,to{opacity:0;transform:scale(.2)}50%{opacity:1;transform:scale(.8)}}._waiting_866n9_14{height:27px;display:flex;align-items:center;gap:2px}._dot_866n9_21{width:12px;height:12px;border-radius:50%;background-color:var(--color-text-primary);animation:_spread_866n9_1 1.4s ease infinite}._dot_866n9_21:nth-child(1){animation-delay:-.2s}._dot_866n9_21:nth-child(2){animation-delay:-.1s}._dot_866n9_21:nth-child(3){animation-delay:0ms}._list_j4z17_1{padding:0 28px;margin:0 4px}._ocr_j4z17_6{padding:0 10px;height:30px;max-width:240px;border-radius:15px;background-color:var(--color-fill-dark-5);display:flex;align-items:center;gap:6px}._content_j4z17_18{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;color:var(--color-text-secondary);font-size:14px;line-height:20px;letter-spacing:.25px}._tips_1pmeg_1{width:fit-content;height:fit-content;padding:8px 12px;border-radius:12px;-webkit-backdrop-filter:blur(32px);backdrop-filter:blur(32px);display:flex;flex-flow:row;align-items:center;gap:8px}._name_1pmeg_15{color:var(--color-text-secondary);font-size:16px;font-weight:600;line-height:24px}._ocr_vr0fc_1{width:100%;height:100%;position:fixed;top:0;left:0;z-index:var(--z-index);display:flex;align-items:center;justify-content:center}._screenshot_vr0fc_15{width:100%;height:100%;-webkit-user-select:none;user-select:none;-webkit-user-drag:none}._selection_vr0fc_22{position:absolute;border:1px solid #0a84ff;transition:background var(--animation-duration)}._background_vr0fc_28{background:linear-gradient(0deg,#0a84ff1f,#0a84ff1f),#ffffff26}._overlay_a6o2e_1{position:fixed;z-index:var(--z-index);top:0;left:0;overflow:hidden;width:100%;height:100%;background:linear-gradient(0deg,#00000080,#00000080),#fff3}._chat_tyuwx_1{margin:16px 40px 24px}._quickinput_tyuwx_5{margin-top:12px}._quickinput_tyuwx_5 ._container_tyuwx_8{margin:0 16px}._filemode_tyuwx_13 ._input_tyuwx_13{border-top-left-radius:0;border-top-right-radius:0}._tips_12dtt_1{margin:8px 16px;padding:0 6px;display:flex;align-items:center;justify-content:space-between}._item_12dtt_9{cursor:pointer;padding:4px 6px;border-radius:4px;transition:background-color var(--animation-duration);display:flex;align-items:center;gap:4px}._item_12dtt_9:hover{background-color:var(--color-fill-dark-5)}._item_12dtt_9 span{color:var(--color-text-secondary);font-size:13px;font-weight:500;line-height:18px;letter-spacing:.25px}._commands_12dtt_32{display:flex;align-items:center;gap:4px}._home_12dtt_38{width:18px;height:18px;display:flex;align-items:center;justify-content:center;border-radius:3px;background-color:var(--color-fill-dark-5)}._highlight_12dtt_49{padding:2px 4px;border-radius:3px;background-color:var(--color-fill-dark-5);color:var(--color-text-secondary);font-family:SF Pro;font-size:13px;font-style:normal;font-weight:500;line-height:14px}._list_uepka_1{padding:0 36px;margin:32px 4px 0}._chat_bj1a8_1{overflow:hidden;display:flex;flex-flow:column;margin:0 auto;width:100%;height:100%}._list_2lt51_1{padding:0 28px;margin:0 4px}._asked_2lt51_6{padding:2px 8px;border-radius:4px;background-color:var(--color-fill-dark-5);color:var(--color-text-secondary);font-size:14px;font-weight:600;line-height:22px;letter-spacing:.25px}._input_1f3tm_1{margin:16px 32px 20px}._constraints_1n6qn_1{position:fixed;width:100%;height:100%;top:0;left:0;pointer-events:none}._kimi_1n6qn_12{cursor:pointer;position:fixed;z-index:var(--z-index);width:40px;height:40px;right:12px;bottom:10vh}._changelog_1n6qn_23{cursor:pointer;position:fixed;z-index:var(--z-index);width:240px;right:12px;bottom:calc(10vh + 48px)}._toolbar_16krs_1{width:24px;height:24px;font-size:14px;cursor:pointer;position:fixed;z-index:var(--z-index)}._toolbar_16krs_1 img{width:100%;height:100%;-webkit-user-drag:none}</style></head><body><div id="kimi-web-extension"><div class="_constraints_1n6qn_1"></div><img alt="kimi" class="_kimi_1n6qn_12" src="data:image/png;base64,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*********************************/X3KYupm3Cf8KM/BJSps21SA3iq0tKfBmNoanpU4rXEj+hjvxQp4JF8xJ9FjjVky4/oyMQghWbwCCwoqK9jEYtTCmR4ylR5FFDIwu+nETJGhtVFjgk2EcoC4gzKBNC9REjRsqot+EhhK8Cz5tyME+iCq1JCCQaSDe/2Lq+hbV2xSpu0xbBFbSrFH7qs2km5RnahFBB+Hj6Nm7cRBb+T1stfCDkr33tbsqygKW23rJ5YaihmFfO77PS2QjZIEiAMVEWBoLl5e0k/y9UrQhQOTVYhCCnSR2pPTShY0bSFVmJBOrqavlzmWT+ysqyvF+JnAfEURhgH8wfUFyhYSCfw88YejmIlOX997dIOVhdHYd/5WXyXMO+ffuJEiiXgN83MC1cKYoSz+0ge69VBcOuOMp3Rr87yyZSEmveyfkcFnkf4wnxj5JRK4YVztyWLl3SauGjIY6+995vCsgSxs6MfhFyxhfQVVpq436PSZUeYkIzWY2GTnEYp6STLvIoJt5TXwvkHgQNUdUPRrkifFV0WAZYgtLScuNWciaaIUkfI0QU8imafJqRfhoxYphmNvnTJZeMoc4y7YxktIOgwszhffv2CuEDBais7CKWAegfitmVw8Z0K2J343DP8wq/S5Z3pYVuX10rYU8T+/yoeFQQNDa5+56/Nnv2bPrlL3/BoVOVlFxpnJ3RZI8XCh2LEVh36oQ8xKqhnnPuDXkmWjRdKwkoebCUJqI0LewKTgtDPUNz19fnTJLKFyIJPIDFE5pRJPHvwiGY/YAD8D0MAQpA4d8nT7qSNm3eQHv27pWRDdOO3wMAIi+ABgUAU4j9hw4dIqzjnj17CvqgiALY0eyWVQdF9iFnu7uunjvaLZkSOjcaUhIkOkmVD0AJsPDiSy8toE984lPSmQi5IH/fsHda1eNJ/F1eUSIKcvLkCcMRaF/ZhaDjtQm0z4AbQNtqYQjQf5mpFFYuQXGHXWzKmHv4eFNRBWsCDKKKFbDbOiyupw8TRTfN/GjE8IHowahH7I8/cAl2qfkhQ4ZIYSgmj5SWlBTcfxEFcDe5vrkplG4AXfTepDE9Lc2O6/6aAnqBc56WAsGz0zCr5sknn6L77//fci0ww9rimj0dYXVC2SomAMFTYpQ6K9m/0H0iSRQl6HwAySR6vilH15VCdQUTkzaWs2TMubWuEhYJeEHPr1PRoQSHONbHbK3jjNeQVezdu5d52HVPWVcYox24AFYAkQAeYjl06DC68sorC+69oMfBSxeO5LQv8FLvk0DPS2C+tOK4rsDMkjGd/UE3FIlu2rSBiZUqIVhkGlY2nkouRZj5nIwyCFCvGViGR3te+QItETMMocEEoc2GUyCVvEmrGkQjXL4PdE4BStYlcjDWBSP9BJt0rBIKMAcMg+xfbe1xKQfr0KGjXA0SQLhm7I+aAPwWbgRKYmsV3Oab59hGray8nJIj2gVxxbYl30dFmaITnqMERElMYJIlnjJeESb4gNugQYNo6bvv0p13fkk6FaweWDyttPWMkFjsOZ2ZI6PXN6uBmWZXE4vr/FmgqEbmeL9U3ADci+YYLI2MiAHb4WJg5m3srsLXruzbt7+YdtQAQAkBBFH0CcLn0KGDVHvihAhfS+BC4QaQ0IILeOONN+jSSy9N3Ctk34QLSJvsNKnjpd7bUihP2VxfmTBVbfcYLrYwCDskKuQXPtgGEuWHP/wRPfroo2xW+7IpDaPJmWRAns1jCKcRWALMzn7W/tHRb5Q9gFUoESwhJd2ZvEg1yNvJMTpwEMM3NGjlbzZTLtt1wkk5nThxTMJ0CBtz//bt2y/zAtGGDBkmox9K26dvX77uXrJ99OjR0WuRSb414AGq3S2dOrWnwhFfzA1YH2cRvZo8EW+ombMkX2B5gny0TX97JozxwbXPfvaz9IeX5tPIESPMlryMVICp0DyWXlK9lDMmX+nhyJIZC4davsA8iAoJHQHEVKKhnp832MHMIsp6JtWcoVMNtWQXqsDvQP507dpNav3h40+dqhPeH+6qG2+H9RjJ5FBHthI9e/fgBFEfqWuAUsFlIIHkNpZ9Dc68zd1Y2dk+nsSOSgfYSEsrgu8c0DPlWBYYZqKOi/f1Usd2levsgcB/+qdH6aGHHqK2tkFVg2jlqhV0333/S0YVzHZ9vS3iVPOslTz4BxpdkbbkDyg0mKAkUnYdLNjHYiALJDW7KHSwjZikXF6PB1cCkmf58hWC+OEKYA3g50EG7di5TcLANWtWUS2Hl2VsMbBfX7YGo0aNojFjxhSkg7kdhQVIrC4NwBCbapf+JXNjaetgRrCzZk3sMmLTps0u2BR/TnIGZ8cCPPTQg/SNb3yDX78j07WxxHpb27e//W36zW9+wxFDf04Nq3u0fYF5gCJUM/nTLluj96qTPjyj7EInR27PiX5kKZicmTCigwTYQy1EGKV8gQsQnuKx9EhOnThxkrOCV4tFAE+wa/du6typI/XkBBEKThAJfPrTn+btuxhE1ibuSZ5CNnXq1FH8fqbdCPJg8+ZNRAV0rsbD5JSFKwBSNswrsBb2d84TMqLiRxsvJ5nE8ePH0q233kptaRj1ELyNr7dt20rPPfe8gLtRo0ZSWxpG06xZt5pZ06tM2tY3o9c2T2cGyQJYzM1ntMgjNHG90sehvvetEikewD/gDZuIgmuwE0hKeWAePHhIyKNypn2R8Rs0aKBUB6MrEeL16tVbWM0D+/dJadiVV0ySKmIUjmzavJkG9O8v1UJOe6YIBgC9mCZ2DLr30mAtiJC8bAsLQV5cMxfG+yX4Bd3XOwsPMFPhP6TXIELJyTVXV29loufjohhtbVCkf/3Xn9APfvADVWLJF2RMFGBCP1kzsFFuD8hfCZ5A2Ea7MAaaLB4VmqpgabhupIxzUkgqcw59nW6PwpGBA/vJsRCRoPADIBDhKKp+16/fQBs3rmPrpOVieNrYy6+8LM8WeP75551wNtGW+3yw5e4WkAna0rG7IBaKkR5FFy6PZA0tyEtHChlKPv1Djx0vw2ZzAzlqS7MjP3IlIXxvqXMbHj30nQflWXxnwyX81V/9Fa1bt4ExwgC1jKE7OLIGCGuOACM2oLyUfIVmriLIJOT6YxcZXzdKzoNoShgqeupFgHV1WqsB04+JIRD0nXd+mRH+KLrxxhsliVV7oo727T8gNPH4S8fLubdv305vchjoPmVEesTzanzzFMoIB4BRspMM41p0y2QElKzaMcvAJJZCdesBrQWITqmdRVbgLotYLNJoXoPgY+HHFiY0o9CGUrj26u1bZAEn1AG0tcEarF+/lr76ta9SdO2mriCMsnahgEa4hYaGk5KwQX9poYYLim2/BMoqSh0iGbYxa0Z6Z3EdWBUEtZt4cMQqBqgvv/wKLVv2Hu1loZeVlzIALKX27bTgFOBv4mWXSQSQeghlzfe///3lVmrV7jc9e9rHk9lwzca35i9wrINdDyDhz9ECZ5d4/9D+b7FAG5E/AB/+dIauC6yMIphVFaJl4jjdWl29nb74xb+kb36zVc9ZKmjf+9736N/+7d/EJ2vhqCCBaASHJksUmFpBVJXZKew6ayiIqpnRNRUsPJh6GT6BgkD49rVrVwuww6xkfa0R2rehoV4UATyA1h5W0s4du2jNqtVCDp04dpyP2S592WL5fXOBr7rfDBgwwFw4RTSlNhe4GT+WWGrdCtMFgW5z3EQ0Jy7joOSWRQHfeehh4/MN/ojQNVFiybgCskmB6j//8/9llzBElnNra/vsZz/DLmEdK8K/06CBgygePHqfYFhRa4g0MEaz1haqkgpEMBlRuAikmmUqeFQfqIU6w4YNFdmAE8BoBmGFeZwTJkwU1929ew/q3q07s38oFhkq37/++mtUztlL5ALcxtclD5gSCTEaTeGAHiaDhRmsdrEDsyhy6IJAj5KPhnFjfzvFyUvsHy8Gaatq8hQ/+r35LgCj/kGM/ITiZM0hLDNnldCGqV5CMLgnWAMgaCjD2Wif+cyneaSuo6effpruuOMOpmvHiik/yaSNVudYYfuSW4CQ4RayGY1aEPZBwBo14Ii6L64Xq4Ai2YO43mb/QPX+6U9/ktnC+HzTTTdzhvM2cReIFL7ylb/m7GHv9EMncbyFtqfgKxa6XyLGLCvX8EUWeXB8dfxEzDQ55II/e+FeYl91x6njWSsR1do3r2Hke/b4JixVEGX3CBPniS2EvQ8iO32s5uhhuueb99GXv/zlaLWttraPfexj4hYWLXqLefi3JC/foUM7mSGk5lgjAoRpDVIbqNeF4hMtLjFT3k2GUZQkmxXLsXz5e8IXoMEd3H///RKiwr1gfcBf//qXsh0kEfIBWD8Y1sBtdtBLjwMIppNCPbv3oHgGqwrYAsJ45m0auFmq1/IBtrPtkuoOMWRGavQYFnlp/kra+luKfhvPlLWj37aYhtZ1iu00bI23hc0LAqkA+tnPfkYTJ048K1GC27BgdK4xR7V1x6QCKC+jXh/60K4dFKPCuFq7IIRGD5h/mA/yZhnYWklH28JPcBFIBKGId+2a9fT222+Lkvm+Jq5ee+01uTcs9NGvX7/E9fD25fYR9NGQ44M+6+4Ef6P9pSyVnasuPwlsjaAL/JKMX8wm2tW6ddkUffVS/tqCwtOtXV2suSRK1kQjlpnMpM6NE5RoeGhm2SDm1vQ3FlzQEGnXrt107bXT6eGH/w+drQZzjuqihvq8ScnCzNeKMGtrT4kQIVSdT6iLSMENBIHiGISF+bwvmclu7OPtMcHawl0vX7GMR3tXUwdwShaKRsEorMC+vfupqqoqfUmRy48UgDtlnrvHRRddrL7fDyiieD2dsBCHcKQXGLqULhnM4FNUBSRjz/zWs49lS7sKtJZwAdaaZAWpynETRFR8ntCswaMEjY4umcXLyqA1eQjZdHvHjp2EbXv44Yfok5/4s7NiDTTN61F5mZp+CLq8vIOQNWAFwev37Nlbzl9alhWgiFGPOtNOlR00c4g6Y/b7Y8eOEbKuffsKUdZjjPBRb4iFIK648jIJDTdu3MSAdC1dccWVsiCFnSRqGyveE9G12TfMGUMrEnwAsIAuSxKYjksLjih2DTHgksSIsyR76D5nN2IOXYthtoctYQM9IjcqCe0oNxGFgyd0OpdZRSSawZtldFymUSzvi5HYv38/mRl09Ohx8bG/f+E5mjHjesmlt7VpClhJHEzdxnVjAW7wA1jmDWVm8NMnjtdR126dxW2g5uCKKybT8JEjhN+H1Vq4cKHwNPZxMVCirVvfZ5awL23csFmAYGVlJ2EHMc0fAxHvnVbDLOZC+yHqpUceeQTCT7mBIToxMRHyxSGeZx/yxB2MxEVkAUL3UW/p8JAcQTtAMZFMalaXRtSJtrxRiYzU5YfmvBGR5ZkkTFTLl5cRJQszyXdYduWYKAniatTug6vBbOmPfOQjxKQJtbahyLNDR338O4AZQjYIEImarl27yErf8N/9+vUhnR4eyvaLL76YXlv4Kq1bu0GprIwWmmJwgmTCb6BEiC7+9Kc/0s6d23n0bxRA2I//du/ZzQp0ReJa0pY+Dbt/5n646KKL5OKVR06Gg+bWSF1AGDFbkt93ljyL/S+R51DBBeAxbBkPIORUlJsgZeDkCaCBKb7QqV3inkK7iocX3bYqrERA7FvL5ffwsVgACvdjl8dH/h37f+tb32Jkf0srXULI4eAlNJB9cd3JU3T40EFZyg0jH1YVsXxV1WBZmUWmjbECHDp0SD7D6HZmF4Hv+/TuQ8OHD5XKIGT+0LBfz57dZYHKw4drWMG6SSHqYfb/H//4J6KlYuJ+8xKDPKEAxjQk3ABKiuGTbOWO78cLRemadfFqViiYjFO91vxbXxzGiN/BC9GFtTAZpMSkVSxf191PPIEEdGyjKkYKW8A6AfRZM4oRg6OcOHFcOHrcI0qyUJixa9cOEiKHt7+04CVZHxAxfksahLxl81bavXsHHTt+VJZyRU4C+AMjGQqA1xkzbmQr0V2uHcANkz5xP6hDuOii0dSuolwQPZJCUAjU+4OOxvFAAuFe6pluRkYXCowKIFiJ+L69amYtT2sB0B51P6CiVBcr1NBJV99U060LIqoIRCjgsX0tdrCC9hLIPs4RxJZBTXVLk0Huo1hDz6zG6VoVoH0UU4S6t0YGcdYSa+jgVmDqt23byQCtnP2pFleEpmQbqVuMSCg1YuzKLkyx7twj5Mqdd95VbN2dog19tn//btq1YzcNHzpczg3hoEwceXxwBvDvq1atlFBv/PiJUtwBxcCydL169+AwbzFdffU0YRHXrFnL5n2XsIt79+2lSydMEH4AlnrUyFEyX3Do8GGsLH3Tl7IwvaFAAdI+AhqHp1JJl/PIKC+voNKS0mRuwPzpREazdp3dlhj1MfcfWqbOSRF7LVgqznIAvmNxEg7EKIVl/6JHv3nxY2EBymDlZBmXfKOUXEkxZtYC2fgP/vqkmN1ARt6TT/2cGbnR9NWvfvWMigDhwpVccsko8f+IMlDTj0rd2tqTtHjxYnr//a0yvx+AbfPmDTzaKwTQbdq0ier5fAjLUSJ+6NBhGeGNbD0+wtaoavBgYQKHcHoY8oFLmDZtGl17zXTq2iWJ/tndPVhwbekNyBBRSlMmT5kkNw5/BICETvLMA58jU2/SxGGCCCKKwZoXh/8UxpFAGO8bhs3HALGC5ePIwnNAZcT6BUm4Ef1WJ1xi9S+Z9MGKcMnFY7XiJm+vJy9l4JoM0xU5JUnD29uVtRfc89RTT0vB5de//vXUI/WSrQQFHYeOUP+BA+jmW26h1WtWC0q/8sorpMATi0Jgbj+sA8Bip04dpHgD+Xy8Aow+99zvOFLoJH4e2OXlP/6Rtm/bzpbOYyvRk5WmvSjN3r17BGOk2kJL/ritKeYFmjLdfgCx0KlTF0kyyGLGnlsASVLUoBktuyy6WxqGEQffnEuaY7v6BpIeEsNb19Hcpokka0ks4rBNjxtEuCDKBoapqiUP9Uw+g7MTLJQVYkol9PUaOK1aIbN48/kcxeXZvuyDUixM7sRoREz+05/+O82b9xt2Iz0E8IFRvPrqq2VmDubu9e7Vi/pxmImsHR74CJ9dx2Z+7do14u9R4AE2D1hj8OAqWrLkHVEspHOxsANIHfj7kSNH0m9/+1t2E+OZEl5OR5jqhdUABXz7bbezIpeyq+oqxy4i04LmURPt3nvvfYUcJYCZ+8UvfikCB7AA3Qh/FS2l4iWzfDp3LjbvMUMXOIjcZAOt4HgzHgmnKN5dYs5zhKZgsbp6M8X669QlWiXzbO2CSUrZyMRZjAoRjmbl9J66dq2kvXv2SQIMnLwNe2PQS5rJCy3pRBI5qIuop3blHWmohM5ZWs2pWKzahX6CEEE3VzIhs3r1Cnp/y1YW8hA6xIKF34YFAAfQsWMHQfcjR49ixN9fQnCsSbj0naWySOUNN17PVuA5IXjefPMNcUvIEsKdTJ9+Lb216E2pBRjI2UiAzEjIDP7Ysg+mIq1J6D1lypRt/PJ5+xlsFQALMkwwfRgZtlPceDz5uFa7XQWUDAPtXkZwEtKV0BH2w2Cz9Jl7NZyoOSKvR2uO6eeaI+aZPI6gPSvoGBjGzQGk2N0PoqvAJM88YxZk5EqyZQK88kG81LvvU1TgqSSYLhNXIkkZ8wwgFGxmynXlbuYVQOCU8HuUbF/Ccfyhw4eoN2MohGPAR1CIpe8u4/0zsrBDjx69hAFECfe4cZfKVK7u3btxtHBMavzWrVtPM26YIZEYsAcwAYpBgF0mT54s2AEZwd2798hagHhySZHKn2+89dZbiYzvGRWAf1DNSjCd31bZbUg5YmUKrF+XkzVzMoKcYQnihZLTlb4eJUqeiCg54cRJ19r6erJuxI5+cn7vEyWWhDfH9uIFo5NVRvFvtaI23g6ELw92kGVddOEmuQbPCt6uAWyXaNdrk/n8AhazLGxVliDU2b0NjY2kK3bXCCDDusJI3/76V7+SX2NpN9TyAdjBNcCCYF1/vC5a/BZd95HrqGOHjjLIsny9ndiXz+fwc9y4sbRg/gIR8rBhI4T9Q6kXZABM8LGbZzKNXCbWKJZFNPq/QE20M8HuhN9AvAw/VFdXL34PKUpw0RoBWE/smHuyI9MTzKBVwVlKMoPWQpAJ23JR5GBDRVUKO09e6+3iUjW7YnbWIYbSiSid74iCDLcaGQKwC0zJk8zCnMl/6HVLzkBIQnsstTKItzHRo6J9GQurk3DxmFCTy+uTPU+e1PUAR40cTcOHjaQunStpxLBRgvhRxwdhDxjYn0d4T8noYR7/8BEjBBt0ZmEO498MZRcBSwHaeNbNt9LuXXvE72O0Y20EgL1ejCtGjRohFmPJO++KWylS/FnU99t2WvalmBXo16+/WAF0HjoTmqqramgVK268tLSdMoOefeZtHOcj6aKFD9a+xmDM8vgCMD0ntezZRZn1gRAortRl2MzEC0+/00JQU4Tq5cg+zCp+IENo5s3ZpJWxEF7enNdao7yzZEFefLHCAS14AeDt1rWHKD/YPERFyMbheHCNJ0/W8QhvYNCn1K6sBspJn127dkqJFkJphHOIqAQfICt44qQAwHGXjqPf/Po3QgW/9tqrwhd06dKJVq1eIxEDQscNGzaK74c7mTbtGv67ml1BNX/Xg9xACiE9j/6/pdYqABrHlK+y/7vbfobvgZZhTroudhzPi9dHnHji36DlWNEK/QnaNTSzYu36t76nT9fQpVMyZrWQ+NGqYj+iUe6TJX5wHty4hmMUpXbjNX5CsrNpIwFTvOJXXNFkk1zWOtn5DuYKZDe7b9bU8etn4EbBDqL4WQndTjLFW1FRKufEiL711ltkyhZcQT3H7Du272S/flzm9QG5Q5CY3Ll27Vq66JKLqQfTubV1J2QSKVYCWf7eMkb/hyXxc+PMG6XKdycfY8CAQbJi2KBBgwXoLV68SPrDThF3G8vpJk5k1bRJAXAAtgLohel2GwCL+q9SMUUwibIuTV7XuM3JOni5KE2cfjCkzolXAQV5ywxYoKhRAdKnAFa5fFx5BOIGVbIYZeXlpYJD7BM1bEZScxXmAQsRig/VPYQxbxHjEU/2D60bCd1wNcY1qriaE8Hzh7QuDzNz6uTeYRkhgD2cgBk2bLiYdzy+FWb6/S2bWQF2CB6ACYcFQ/iICRs33jhTOIgX2b8PHjxIFASx/7JlyyQZd/nlV1ADu5Wd7AIwHbxq8AC2DK/Lk0mR6LFFonDNqfZgmvYt1ppFvTFQeiRdMYSFlK+5ZppQp/CV8EW2IkWmMxkBYLaqnMg3qeIwjLgELGSIukNJzIRuIkmXVkWplMw5MPPkQlM2hfOh8EFHrBGRzNW2zJ1VhKyxAnbalnPbkcLodepyrOYhDebhkr55wIW1KDZkxPH79RtgFl/yhafvwiHknj17xbxDSRDWATRjIieEjlDtmquvoe5duzM2GCnr/MFVgAd46skn2QUcFwyw5O0lkvQZN2683APcAoSdZYvqs6K9+MJ8Oe5HP/pReuedJcJeghtwG2TFRNAj1IzWrAwMU5Wn+IJRRfp5uw0dght7//33xR8jfIFAO3XuwNtrxUrg+759ewvShpUwF0fWzw4aWCUzXmRxxLwXLYUOYISQTD4bVs+icI3d9fEycEHI20NZtBQ7FqjiBhMXeGHsG+2q5KFZxlXW2dcFmys5VBMAJ493SSqrKku83Brm6aFAExFBx44VUnsHUzx8+Ah69913+LutAg4xKwkovU+ffnxPx8R3I2WLWb2o5kWEMIG5fPTRkiVLxJ3gD2sS2ZU98YSynqwU+9m6YC2AsWPHy7VhsuiECeOpCIF6+9///d+vp7OlAGgAhBx3duFOmGS3gW6EYMENSElTLn5kGkIgdBoWMYYgYf4qGQ3r8+zKmDSpiBYztmjc8z2DvHNmzfsKOQbcCbh0XQP3lOwLdIwRaUGdCkcjEEvdqtD0ad3J0nYyCqSLP8CVQMnq6+tMibbiPcl8RhxjSD2664qcsHSotUNsjwoiZN6Qpt3FZhpkDrJ3qNnDBBQs2oz7QcQEzh/74v4xSJATAAbAvcBigNmDOwEf0KNHVxb02GiGb5bvE64hy8fB+WEZMJO4o1kLyDbuh0c5q/sTamZrfvaFZNHhB9KuAKtOwP+IyWtXJtU0vXv3obh4hGRUIW7WZVAD0Xa8hymzU5a78w1r55aJcGBB7OIHuEyET/K7QMuj9NFogVlo2Ys4erUSmWg+QxCoAsXsY0wUQVnhZnRpuIzhCGyOQZsNSTUBpgswtmvXXq4VnP3YsRcLYq+s7Cazb2C9JLRjZQcGwEAAQOvB26BwF42+2CTXFFBDierZKrz66qsCALENo//NNxdJaRfQPawaBhkszw3XzxDFuPyKiTSMlc5tJua/m1rQWpSEhyvgqOBZ7uzP88dyOQB3HDQUqUvw2UDDSFzgAYn65Iv6SEB2VWwIXytdT5knbIai1RgpwAwAebAOupauTrFCR8Iq+F4M+CQpRSasM6t3qGCdRRrI1ixkom0YZQIwzdq5sCK5XIMQQZ4XRLE0rrFduwpW7koJ82AppnBiDAydXZUb/P24ceNIgWuWCZqt4gaWvbdUrAQmcqxbt1EyjBB2125dRTmRFcREkvZ8v6ij2Fq9jUYyjsLyL68ufJV9/Ex59CsYUTCwI0cOl2XkYTH6c5oXlieVPKvh808+E+pvkwKg4QRTp07FM2Wihw+iM3FDqFcDsAnMdCYUPHhSaVMqqBgs1uHDRwQF6xr8mo6FG4EyQKB2SXQIRaYyG87dMnJ4Vh6mSUFxgMI7d+4oSNz34mhBjY/OqrXhY+QCMOXaLJysxar6OxRjwkrhulF0CVMOgZeXl4gLs4+ew72BuwcdjXw7ANgrr7wsAlm69F1B5cjGDRs6QjJ3GBiorJo16xYZAOANQO/ClyMMvO22WZLShRLs3LVDlAKsH9b0g3W89NLxkofBoELBCqqSQMsHQUH53N8y6p9PLWytmpMNXjmNB3DjUALMtEEVUfv2HaPFJlCzdvnll4nJRApUlkVt0JBR1+JTggajDNQyTClCG3Q4FkUcwMkNEEfHGPFCwaAo8N0w35gBow9Iyhj0z9fSvr0WSDBAg6+0I138pnngAppd6Qu/yxtqG9YKCgmXNmvWLBbmPhEgAN5Hb76ZGbndLPQRMtkDQtm5cwdz8lPY1+/idKw+4gXJGiwkAYoc9zdgQD/h/Q8cOMgKsV3AMlb7AqePSZ2LF79NgzkjCB+PdYBA8Y4efRFnYfuyhVkiABMDZTRHG3gqSHqSB7cH2e9/l1rRWj0pf9GiRfPZEuBpI6PsNsEBfKHIhNVyVguVKVddhYTFZgErBw4cEgGB2YIQoSC2k7B97Jjx0vm4YQgd6WcIEvHyju3b2PeNE0uBjBkwAkYp4un4WTgZsRRQLlucYl2QJnhU0RQvxIAVv+nSpYcoF0qoL710guT2IST4/K3sh+Hnj584ysmdI9F3kyZdIeYZgoPig6cAAARqB6JHMQ2STH379ROkDwUAT4ApXjbiQeX1mDFjZZQPHDBQVvIAYMR1rFrJjCvfL8591VSklodKX7gNbB8L/yvUytamVRmYiFjAHYrVRaLVh3r07CGlU3V1x8VvY8kzMFbr2Hdh5ID+3Lp1m9wUBIPKVwgFggS5g07BcqcwsQBRGF0oxEQNPIgPdBSEAuIFcTjKufS5O7A2ebPEmj7owT6WDaYYEYU+Rate6hvwHiXa2Ac8PoR78cUXaWTDeGDqVVP5mtdJVu6qq6eKOR7KBM/2Hdtk0sUmBmirmZ7FtcOcg7HLoUyb43wQNrBiEBZWWwE/gJk6uF6EgFOmTBUmFVm7o0drZLo3LAPIL4SFcKm43lmzPiY8P+r/0HfpBtDHx7iJXe8pamVrkwIYULjAPHY+WnYeYAfPrIWQlr+3go4eq+EOqmSma7CY2169+si8ehQyQEkwEweEyR7k4g1NAPR76fgJ3OHbBViiaAL7gH/HkzLgZ+ETIVBYExRhAD8gvQr+wU6hQkeCNKpjmnU0I/CRI0fJKMQ+oKFRuLFz527BK3BRAG6Xcnx+iiMXzMGr4pGOCttKjuX3siAlB8KXiGvBSIWVq6zsKqTYDlbOE3xcLNKA6AiKgQQOrAQsHY6Psi4oK5QaeYD+TCht3LhB6iDe5eQPsMDtt98uIeFbby0SMIwHWNkHP9gm6/tkMtc+/PDDe6kNrc3rsgAUIjJIKwFSxqhwnXj5RHYJaxhkZYQ0QpUtrAQZQIVpy7K2LRNI6DygbjxBo7JrZ3EBYORAicIHgnCBuUWnYHThFQQL9gMRhRi7C/PwGDGDBvWTkBQKAN8O4ARghbx5vXlgA5ZZRZwOMgX4BdamC1umyZOm0G9/M8+kumvZIuRo8pQrxCy/YpZdAZaYOPFSwSpwM8jLo1wbgkSYV80jfNKVk+ill16S6VsYzUgDH+PBgEgBCldRUS5P/MC1X3vtdbSeweGNM6+j//7vpzgKuEnYQgDhdHmXFX6xEq+WtrYvzENNKwEAEeLnMWPH0ErGBeC5MyyMO+74c9rELNoJJkPQgeg8+LcjR46K6dy1ewf17d+PlaKSDkhI2Z47ewJt2LieO+ZmidchGLgIAEBgAvhtuI0+zDxu2LBeqmJgbm+88QYZTeAUYEanTJ0sIG0871/OnVvJuAWlW0sYwY8aPYrWrlknnb5p80Y5HmbtIuybNHkyPf3kU8zI9RJA1445DNTtwwogMsGsIlTq9mEl6MHWb9v2rRKvIgdgWT5YMliu/v0HCu7A5337Dkjl9YsvviBl3wgtsawLuBONcpKA72wKH+2sKABaU0oA04Xih6ohVfKETAhvCLuCgYMGsCAuF2wAk75DfOsIuokFjHq23RxqhaY+fu2aNeI/165dLyNn6dJ3qEPH9tETMqBgHZhNg++EYsDHT59+De1l8mQAAyv4bzxmdfiI4dSfSatXOVwdyPE5snAQxNJ3l7LVydC69etp+rXXiqW46aaPygQO7Ne3Xx+q4PDtMKdwAew2sWKpRerEytGDlfRgZNHgw3sxQAXx88ILv5dRDsILrg7XAWuH0BXv9TGv9Zw5nCUCnzhxnLgNRAmwJCWp1b3PtvDRzpoCoDWlBALSmOHrzv4ZIA2uAIUUGBmoLbjmmmsEDPWX5c85t86mFjH5NgaL27ZV0+AhQ9hanBDhYnIkMMZ2jq+nTZ8upMhwBmfwnet55F/JZncPj0SMtNs+/nEmZN4T1I0qW+AHdCwUD+eAoDes38A5+PHCBFbzfnfddaekqjEfEIsvISsHFL+CrQiWXoHLgMkGlwEXh/sAXXWEowOAg/YdKhi9r5QFH2DqEV7GhBJHP3x/XbGKBysAiDLgAuQU4ILWrF3NbmiqKEwR4S/nfrzpbApfZENnuUEJGK0/wReL8HCU+x0KFjG6wX1DKQ7zqIAJBVuGKtnnnn1WKmG3M5iCiYUPRGcM4FG74KUFTKNeJHE/BNyVQdn+/XtpBXd2NXc0iiK2c6iImB0RxSc/+Sl6Z8m7Ysbv+PSnmUM4KiHk9TOup5/85CeCIdD5OAeWU0WkUsHKuYNj8OUrlguuePHF+fTXf/0VYeMWLHhJgJ99CjeEDyuAhA5YxC0c6pax6d7Gv4ebq2WXozmAUrOI4ynZD9aw/wBOHbcrl2sCqMR1IxmFkBDXla7qQajHbvD2tgK+Yu2sKwAaogMmi55J1xGgoWiyjIUOS3CcSQ9MYsBoQ/w/ZNhQOsR+/WIWIpTihz/8oYSJV8nz8UplFPXo2U3YOwDA0Wa/vn10ahdoUqBxuBnE6ldOniScAkJPPAZmGANORBVY4g1hHEYmFmTCiN23d68o2wSOCt5eslh4iI4dOnPyReldxPCYR4DrQI4fwkfJ9x/+8AfhJEDb4h6wEMRJFjisB/AJzt2RQ0TgjTHjxoqPRwg4beo0eTg1mEQoLfh9HCfdkNxBTV9bQr3TtXOiALaxEixkJcAsSzCG5XY7zFsJJ2AQL2tGrSPntt+REYGlXU+crKVFHAK9zyMOCZAaBooLFiyQoserGMQdPsLugn071r5DJ2N5NAgFGGKgcSPyiBQ215OnTJZQDhYG+fT32KSjrgDgCr4cPD7Krr505530X//5nyJUFGnACowaPVJ4eRwLAO0oWwIgcpRgr1jxnvHrx2VuHtwZwCP2BTYAeD3OYSpGfiNTwPX8N278OJnAeYoJp2FDh0uqGCAVSuzO4TOthjHOV3gQtIrha247pwqAxkqwmEf5M2lcgAbhgx/HevYQIJ6uvXbdWprJAkCoBd6gn1ntAqHYjm07ZL3bqkGDhVlEbgGmGKMHfhmxNwQE9wLLMWDQQAFl8377W5o/fz4rz1S6GgQP8+2wPBjxIJbGsmBQkrb0vWVyHEQNt3DYtmzpMtq+TYkfEFEIa1A2jrxGr969xSWgMAb8ALABzo17AmBFuAveAe4ODCNA6nHO8kGZwQ3gyR/4LSj0dAPYQ2KHR/5COsftnCsAGnABK8Kj6fwBGthAmD4oAkI8hEuTmSmDAuzkSADtPRYIKFsIEzH+K6+8IinTsWxS/8gmGIoAkw9kDYF+5jOfoVWrV9GTTz4pIRwSKkjPolBjxowZsi+yeTj+gj+8JBMw2zPKh+8FGHvj9ddlKRZYEwhPRj5q7flaZ978Udq/dz8D2m6yGhiII4SysGpwKSCkECZ27dpdYnyQTmA9oeB7OK8wjM8rU8XZxRRbvhUmn/39n58Lf1+seXSe27333judb/Lx9PMKbUPmEGTNKPaLR44eoW5M7HRi3PCznz1OM66bIf4U4SRCvDEcxoFLQGHkd77zHcEFyEgC2EEYjz/+OH3slo/J6BzMiqPUq044geAwYrNseue/+CLt5Kjix//yL/SrX/6SsixMmY3LVgJu5W1O1owZcwnjh50y0WOvEEqY6TtMLAvoYF1LISOTNaDECEWxOhdAHo4BawOLFi/Fm2wY9dwnX3BX7zgf7bxYALehsghRAncaMjjT098jlgZ7h6pi8AO/+91ztJHDu/vuu0/CrRdeeIF69+kt1gDPzkGHw/RjvhzA5F133SUCgALcdNNNIvCnnnpKWDwIBD7frtSxgSlYRB2gWgE8f/HMM7IaCKICLK+6eNFi8fNDOLsJsgrP6Xuaj5XjY0Oh6li4FskjREU4+/vf/15AHiIOKNwnP/lJKRCBxUnP2HHag6yMX2huGdfZbOddAdBMlLCQ/fATxhKMSu8DgmTr+1vMs/yyOjOWBQBiCAUoYPkQux9nEAgwN/OmmeJKQA7Bj9saBXD+MLn43bx584T7h4mG/x5sFll4e/FiAWJQHGTt4JYOsuCvuvoqsRZQGiw1/9JLf+DoYKD4/EkcYcByYBYPgCgUCqgeCofqJZh8i0nS5dpOW8j3di2qd88Vyj9Ta+m6bGe1GVLjdh7dn+fXucXcQmepeetEvdmXg0zCFG3MgAEjByHDFCPphDAPMTSKNaYzQfTEE0/Iun+PPPKICAbZuH/4h3+gl19+WUalFrH2EAEB/YNogtWAcgAjICcg6Vk+Hkw7Urwj2epgYgdC1XfffVcU0BI2GPFQQJh+KIw7PatIW0iaw19IH3A77xjgdO10iuA2ECtYCh1CBBF07733CIoHozaBRx1WAoc57tCxg1T9bNiwIQq1YN4xWmGyUXiBkYpYHqttIhePMA6WAb7+H1l57rjj04wlHpORP5AJqSVsLcA7zHt2nixOgSdzwLog6jjNSLdtIV0ggrftglIA2wAU+WUuFcEIxVovTtBITWGgU8JBx2IUgjv41J99ijZv2CQuBaN70qRJtGrVKlkfGA99wJNDf/zjH4sCvLzwFbEiSNUCSC5etEhA6R6mlUEbY2burFtukcWlO7Ll0LWFmtUW0gUmeNsuSAWwjYVSxQTLA+yTrzmTVXAbzDL88G4WGniD60EuMRYA3YsoAXPsWclkUQUQTwjjVq7kUDOjE0IQ+yNERAUuavp0QmeJM9WsWQ3FmY+a+XnL6QJtF7QCuO2ee+65jTsTZBIeKlRJF2arYUWdx9f5xIU42ou1D40CuM24iM/zH+qxx9MH2Mw8CWRA5zGgXP7AAw+cneXGz1P7UCqA2+AmGLiNZ9Q9nYVgFeJcWQgIt5qF/iq/LufzLuQoo5o+xO1DrwDFGkcT41kZoATjWVhV/DrIfK7kz5VN4Qk76wlPUsMfK9VR+55DxOUfdmEXa/8fQ79G5HHSfbcAAAAASUVORK5CYII=" draggable="false" style="opacity: 1; will-change: auto; transform: translateX(-25.911px) translateY(0px) scale(1); user-select: none; touch-action: none;"></div></body></html></template></kimi-web-extension></body></html>