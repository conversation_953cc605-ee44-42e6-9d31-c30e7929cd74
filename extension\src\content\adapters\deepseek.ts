import { AIAdapter } from './base'
import { Conversation, Message, AIPlatform } from '@/types'

export class DeepSeekAdapter extends AIAdapter {
  private floatingBubble: HTMLElement | null = null
  private archiveButton: HTMLElement | null = null
  private inputElement: HTMLElement | null = null
  private sendButtonContainer: HTMLElement | null = null

  // 拖拽相关状态
  private isDragging: boolean = false
  private dragStartTime: number = 0
  private dragStartX: number = 0
  private dragStartY: number = 0
  private bubbleStartX: number = 0
  private bubbleStartY: number = 0
  private longPressTimer: number | null = null
  private originalPosition: { top: string, right: string, left: string } = { top: '20px', right: '20px', left: 'auto' }

  constructor() {
    super()
    this.platformName = 'DeepSeek'
    this.platform = 'deepseek'
    this.selectors = {
      inputField: 'textarea[placeholder*="输入"], textarea[placeholder*="Message"], textarea[placeholder*="请输入"], textarea, [contenteditable="true"]',
      sendButton: 'button[aria-label*="Send"], button[type="submit"], button[aria-label*="发送"], .send-button, [data-testid*="send"]',
      messageContainer: '.message-container, .conversation'
    }

    // 初始化DeepSeek特有功能
    this.initDeepSeekFeatures()
  }

  async injectPrompt(prompt: string): Promise<void> {
    const inputElement = await this.waitForElement(this.selectors.inputField) as HTMLTextAreaElement
    if (!inputElement) {
      throw new Error('DeepSeek input field not found')
    }

    this.simulateUserInput(inputElement, prompt)
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []
      
      messageElements.forEach((element, index) => {
        // DeepSeek可能使用不同的类名或属性来区分用户和助手消息
        const isUser = element.classList.contains('user') || 
                      element.getAttribute('data-role') === 'user' ||
                      element.querySelector('.user-message') !== null

        const contentElement = element.querySelector('.message-content') || 
                              element.querySelector('.content') || 
                              element

        if (contentElement) {
          const content = contentElement.textContent?.trim() || ''
          if (content) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      const title = `DeepSeek对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `deepseek-${Date.now()}`,
        platform: 'deepseek',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('Extract DeepSeek conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    return window.location.hostname === 'chat.deepseek.com'
  }

  // 初始化DeepSeek特有功能
  private async initDeepSeekFeatures() {
    console.log('Initializing DeepSeek features...')

    // 等待页面加载完成
    await this.waitForPageLoad()

    // 延迟一点确保页面完全渲染
    setTimeout(async () => {
      try {
        // 创建浮动气泡
        this.createFloatingBubble()
        console.log('Floating bubble created')

        // 设置输入框聚焦监听
        await this.setupInputFocusListener()
        console.log('Input focus listener setup')

        // 添加存档按钮
        await this.addArchiveButton()
        console.log('Archive button added')

        console.log('DeepSeek features initialized successfully')
      } catch (error) {
        console.error('Error initializing DeepSeek features:', error)
      }
    }, 2000) // 延迟2秒确保页面完全加载
  }

  // 等待页面加载完成
  private waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve()
        return
      }

      const checkReady = () => {
        if (document.readyState === 'complete') {
          resolve()
        } else {
          setTimeout(checkReady, 100)
        }
      }

      checkReady()
    })
  }

  // 创建浮动气泡
  private createFloatingBubble() {
    if (this.floatingBubble) return

    this.floatingBubble = document.createElement('div')
    this.floatingBubble.id = 'echosync-floating-bubble'
    this.floatingBubble.innerHTML = `
      <div class="bubble-content">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        </svg>
      </div>
    `

    // 设置样式
    this.floatingBubble.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10000;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
    `

    // 添加悬停效果
    this.floatingBubble.addEventListener('mouseenter', () => {
      if (this.floatingBubble) {
        this.floatingBubble.style.transform = 'scale(1.1)'
        this.floatingBubble.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.25)'
      }
    })

    this.floatingBubble.addEventListener('mouseleave', () => {
      if (this.floatingBubble) {
        this.floatingBubble.style.transform = 'scale(1)'
        this.floatingBubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)'
      }
    })

    // 添加点击事件
    this.floatingBubble.addEventListener('click', (e) => {
      // 如果是拖拽结束后的点击，不触发显示功能
      if (this.isDragging) {
        e.preventDefault()
        return
      }
      this.showStoredPrompts()
    })

    // 添加右键点击事件用于调试
    this.floatingBubble.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      this.debugFeatures()
    })

    // 添加拖拽事件监听器
    this.setupDragEvents()

    // 保存初始位置
    this.originalPosition = {
      top: '20px',
      right: '20px',
      left: 'auto'
    }

    document.body.appendChild(this.floatingBubble)
  }

  // 设置拖拽事件
  private setupDragEvents() {
    if (!this.floatingBubble) return

    // 鼠标事件
    this.floatingBubble.addEventListener('mousedown', (e) => this.handleDragStart(e))
    document.addEventListener('mousemove', (e) => this.handleDragMove(e))
    document.addEventListener('mouseup', () => this.handleDragEnd())

    // 触摸事件（移动端支持）
    this.floatingBubble.addEventListener('touchstart', (e) => this.handleDragStart(e.touches[0]))
    document.addEventListener('touchmove', (e) => this.handleDragMove(e.touches[0]))
    document.addEventListener('touchend', () => this.handleDragEnd())

    // 防止默认的拖拽行为
    this.floatingBubble.addEventListener('dragstart', (e) => e.preventDefault())
  }

  // 开始拖拽
  private handleDragStart(e: MouseEvent | Touch) {
    if (!this.floatingBubble) return

    this.dragStartTime = Date.now()
    this.dragStartX = e.clientX
    this.dragStartY = e.clientY

    const rect = this.floatingBubble.getBoundingClientRect()
    this.bubbleStartX = rect.left
    this.bubbleStartY = rect.top

    // 设置长按定时器（500ms后开始拖拽）
    this.longPressTimer = window.setTimeout(() => {
      this.isDragging = true
      this.startDragMode()
    }, 500)

    // 添加视觉反馈
    this.floatingBubble.style.cursor = 'grabbing'
  }

  // 拖拽移动
  private handleDragMove(e: MouseEvent | Touch) {
    if (!this.isDragging || !this.floatingBubble) return

    const deltaX = e.clientX - this.dragStartX
    const deltaY = e.clientY - this.dragStartY

    const newX = this.bubbleStartX + deltaX
    const newY = this.bubbleStartY + deltaY

    // 边界检查
    const bubbleSize = 80 // 拖拽时的大小
    const maxX = window.innerWidth - bubbleSize
    const maxY = window.innerHeight - bubbleSize

    const constrainedX = Math.max(0, Math.min(newX, maxX))
    const constrainedY = Math.max(0, Math.min(newY, maxY))

    this.floatingBubble.style.left = `${constrainedX}px`
    this.floatingBubble.style.top = `${constrainedY}px`
    this.floatingBubble.style.right = 'auto'
  }

  // 结束拖拽
  private handleDragEnd() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
      this.longPressTimer = null
    }

    if (this.isDragging) {
      this.endDragMode()

      // 更新原始位置为当前位置
      if (this.floatingBubble) {
        this.originalPosition = {
          top: this.floatingBubble.style.top,
          right: this.floatingBubble.style.right,
          left: this.floatingBubble.style.left
        }
        console.log('Updated original position after drag:', this.originalPosition)
      }

      // 延迟重置拖拽状态，避免立即触发点击事件
      setTimeout(() => {
        this.isDragging = false
      }, 100)
    }

    if (this.floatingBubble) {
      this.floatingBubble.style.cursor = 'pointer'
    }
  }

  // 开始拖拽模式
  private startDragMode() {
    if (!this.floatingBubble) return

    // 保存当前位置作为原始位置
    this.originalPosition = {
      top: this.floatingBubble.style.top,
      right: this.floatingBubble.style.right,
      left: this.floatingBubble.style.left
    }

    // 拖拽时变大
    this.floatingBubble.style.transform = 'scale(1.3)'
    this.floatingBubble.style.boxShadow = '0 8px 40px rgba(0, 0, 0, 0.3)'
    this.floatingBubble.style.zIndex = '10001'

    console.log('Drag mode started')
  }

  // 结束拖拽模式
  private endDragMode() {
    if (!this.floatingBubble) return

    // 恢复原始大小
    this.floatingBubble.style.transform = 'scale(1)'
    this.floatingBubble.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)'
    this.floatingBubble.style.zIndex = '10000'

    console.log('Drag mode ended')
  }

  // 添加存档按钮
  private async addArchiveButton() {
    // 等待发送按钮出现
    const sendButton = await this.waitForElement(this.selectors.sendButton) as HTMLElement
    if (!sendButton) return

    // 找到发送按钮的父容器
    this.sendButtonContainer = sendButton.parentElement
    if (!this.sendButtonContainer) return

    // 创建存档按钮
    this.archiveButton = document.createElement('button')
    this.archiveButton.id = 'echosync-archive-button'
    this.archiveButton.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `

    // 设置存档按钮样式
    this.archiveButton.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      margin-right: 8px;
      background: transparent;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      cursor: pointer;
      color: #6b7280;
      transition: all 0.2s ease;
    `

    // 添加悬停效果
    this.archiveButton.addEventListener('mouseenter', () => {
      if (this.archiveButton) {
        this.archiveButton.style.backgroundColor = '#f3f4f6'
        this.archiveButton.style.borderColor = '#d1d5db'
        this.archiveButton.style.color = '#374151'
      }
    })

    this.archiveButton.addEventListener('mouseleave', () => {
      if (this.archiveButton) {
        this.archiveButton.style.backgroundColor = 'transparent'
        this.archiveButton.style.borderColor = '#e5e7eb'
        this.archiveButton.style.color = '#6b7280'
      }
    })

    // 添加点击事件
    this.archiveButton.addEventListener('click', (e) => {
      e.preventDefault()
      e.stopPropagation()
      this.archiveCurrentPrompt()
    })

    // 插入到发送按钮前面
    this.sendButtonContainer.insertBefore(this.archiveButton, sendButton)
  }

  // 设置输入框聚焦监听
  private async setupInputFocusListener() {
    // 使用更灵活的方式查找输入框
    await this.findAndSetupInputElement()

    // 使用事件委托监听所有可能的输入框
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement
      if (this.isInputElement(target)) {
        this.inputElement = target
        console.log('Input focused:', target)
        this.moveToInputField()
      }
    })

    document.addEventListener('focusout', (event) => {
      const target = event.target as HTMLElement
      if (this.isInputElement(target)) {
        console.log('Input blurred:', target)
        setTimeout(() => this.moveToDefaultPosition(), 100) // 延迟一点避免闪烁
      }
    })

    // 监听页面变化，重新查找输入框
    const observer = new MutationObserver(() => {
      this.findAndSetupInputElement()
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  // 查找并设置输入框元素
  private async findAndSetupInputElement() {
    // 尝试多种选择器
    const selectors = [
      'textarea[placeholder*="输入"]',
      'textarea[placeholder*="请输入"]',
      'textarea[placeholder*="Message"]',
      'textarea',
      '[contenteditable="true"]',
      'input[type="text"]'
    ]

    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLElement
      if (element && this.isVisibleElement(element)) {
        this.inputElement = element
        console.log('Found input element:', selector, element)
        return
      }
    }

    // 如果没找到，等待一段时间后重试
    setTimeout(() => this.findAndSetupInputElement(), 1000)
  }

  // 检查是否是输入元素
  private isInputElement(element: HTMLElement): boolean {
    if (!element) return false

    const tagName = element.tagName.toLowerCase()
    const isTextarea = tagName === 'textarea'
    const isInput = tagName === 'input' && (element as HTMLInputElement).type === 'text'
    const isContentEditable = element.contentEditable === 'true'

    return isTextarea || isInput || isContentEditable
  }

  // 检查元素是否可见
  private isVisibleElement(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    const style = window.getComputedStyle(element)

    return rect.width > 0 &&
           rect.height > 0 &&
           style.display !== 'none' &&
           style.visibility !== 'hidden' &&
           style.opacity !== '0'
  }

  // 移动气泡到输入框左上方
  private moveToInputField() {
    if (!this.floatingBubble) {
      console.warn('Floating bubble not found')
      return
    }

    if (!this.inputElement) {
      console.warn('Input element not found')
      return
    }

    const inputRect = this.inputElement.getBoundingClientRect()
    const bubbleSize = 60

    console.log('Moving bubble to input field:', {
      inputRect,
      bubbleSize,
      inputElement: this.inputElement
    })

    // 确保输入框在视口内
    if (inputRect.top < 0 || inputRect.bottom > window.innerHeight) {
      console.warn('Input element is outside viewport')
      return
    }

    // 计算气泡位置 - 移动到输入框左上方
    const bubbleTop = Math.max(10, inputRect.top - bubbleSize - 10)
    const bubbleLeft = Math.max(10, inputRect.left - 10) // 左上方，稍微偏左一点

    this.floatingBubble.style.position = 'fixed'
    this.floatingBubble.style.top = `${bubbleTop}px`
    this.floatingBubble.style.left = `${bubbleLeft}px`
    this.floatingBubble.style.right = 'auto'
    this.floatingBubble.style.transform = 'scale(1.1)' // 聚焦时稍微放大

    console.log('Bubble moved to input field (left-top):', { top: bubbleTop, left: bubbleLeft })
  }

  // 移动气泡到默认位置（原来的位置）
  private moveToDefaultPosition() {
    if (!this.floatingBubble) {
      console.warn('Floating bubble not found')
      return
    }

    console.log('Moving bubble to original position:', this.originalPosition)

    this.floatingBubble.style.position = 'fixed'

    // 如果有保存的原始位置，使用原始位置；否则使用默认位置
    if (this.originalPosition.top && this.originalPosition.right && this.originalPosition.left) {
      this.floatingBubble.style.top = this.originalPosition.top
      this.floatingBubble.style.right = this.originalPosition.right
      this.floatingBubble.style.left = this.originalPosition.left
    } else {
      // 默认位置
      this.floatingBubble.style.top = '20px'
      this.floatingBubble.style.right = '20px'
      this.floatingBubble.style.left = 'auto'
    }

    this.floatingBubble.style.transform = 'scale(1)' // 恢复原始大小

    console.log('Bubble moved to original position')
  }

  // 存档当前提示词
  private async archiveCurrentPrompt() {
    const currentPrompt = this.getCurrentInput()
    if (!currentPrompt || currentPrompt.trim().length === 0) {
      this.showNotification('输入框为空，无法存档', 'error')
      return
    }

    try {
      // 保存到storage
      await this.savePromptToStorage(currentPrompt)

      // 显示成功提示
      this.showNotification('提示词已存档', 'success')

      // 添加视觉反馈
      this.showArchiveAnimation()
    } catch (error) {
      console.error('Archive prompt error:', error)
      this.showNotification('存档失败', 'error')
    }
  }

  // 保存提示词到storage
  private async savePromptToStorage(prompt: string) {
    const promptData = {
      id: `prompt-${Date.now()}`,
      content: prompt,
      platform: 'deepseek',
      timestamp: Date.now(),
      url: window.location.href
    }

    // 获取现有的提示词列表
    const result = await chrome.storage.local.get(['archivedPrompts'])
    const archivedPrompts = result.archivedPrompts || []

    // 添加新提示词
    archivedPrompts.unshift(promptData)

    // 限制存储数量（最多100条）
    if (archivedPrompts.length > 100) {
      archivedPrompts.splice(100)
    }

    // 保存到storage
    await chrome.storage.local.set({ archivedPrompts })
  }

  // 显示存档动画
  private showArchiveAnimation() {
    if (!this.archiveButton) return

    // 创建飞行动画元素
    const flyingIcon = this.archiveButton.cloneNode(true) as HTMLElement
    flyingIcon.style.cssText = `
      position: fixed;
      z-index: 10001;
      pointer-events: none;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    `

    const buttonRect = this.archiveButton.getBoundingClientRect()
    flyingIcon.style.left = `${buttonRect.left}px`
    flyingIcon.style.top = `${buttonRect.top}px`

    document.body.appendChild(flyingIcon)

    // 动画到气泡位置
    setTimeout(() => {
      if (this.floatingBubble) {
        const bubbleRect = this.floatingBubble.getBoundingClientRect()
        flyingIcon.style.left = `${bubbleRect.left + 15}px`
        flyingIcon.style.top = `${bubbleRect.top + 15}px`
        flyingIcon.style.transform = 'scale(0.5)'
        flyingIcon.style.opacity = '0'
      }
    }, 50)

    // 清理动画元素
    setTimeout(() => {
      document.body.removeChild(flyingIcon)
    }, 650)
  }

  // 显示已存储的提示词
  private async showStoredPrompts() {
    const result = await chrome.storage.local.get(['archivedPrompts'])
    const archivedPrompts = result.archivedPrompts || []

    if (archivedPrompts.length === 0) {
      this.showNotification('暂无存档的提示词', 'info')
      return
    }

    // 这里可以实现一个弹窗显示存档的提示词列表
    // 暂时先显示数量
    this.showNotification(`已存档 ${archivedPrompts.length} 条提示词`, 'info')
  }

  // 显示通知
  private showNotification(message: string, type: 'success' | 'error' | 'info' = 'success') {
    const notification = document.createElement('div')
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      z-index: 10002;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      background-color: ${
        type === 'success' ? '#10b981' :
        type === 'error' ? '#ef4444' :
        '#3b82f6'
      };
    `
    notification.textContent = message

    document.body.appendChild(notification)

    // 3秒后自动移除
    setTimeout(() => {
      notification.style.opacity = '0'
      notification.style.transform = 'translateX(-50%) translateY(-20px)'
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  // 调试功能
  private debugFeatures() {
    console.log('=== DeepSeek Adapter Debug Info ===')
    console.log('Floating bubble:', this.floatingBubble)
    console.log('Input element:', this.inputElement)
    console.log('Archive button:', this.archiveButton)
    console.log('Send button container:', this.sendButtonContainer)
    console.log('Original position:', this.originalPosition)
    console.log('Is dragging:', this.isDragging)

    // 查找所有可能的输入框
    const textareas = document.querySelectorAll('textarea')
    const contentEditables = document.querySelectorAll('[contenteditable="true"]')
    const inputs = document.querySelectorAll('input[type="text"]')

    console.log('Found textareas:', textareas)
    console.log('Found contenteditable elements:', contentEditables)
    console.log('Found text inputs:', inputs)

    // 测试移动功能
    if (this.inputElement) {
      console.log('Testing bubble movement...')
      this.moveToInputField()
      setTimeout(() => {
        this.moveToDefaultPosition()
      }, 2000)
    } else {
      console.warn('No input element found for testing')
      // 尝试重新查找
      this.findAndSetupInputElement()
    }

    // 测试拖拽功能
    console.log('Testing drag functionality...')
    this.startDragMode()
    setTimeout(() => {
      this.endDragMode()
    }, 1000)

    this.showNotification('调试信息已输出到控制台，包含拖拽测试', 'info')
  }
}
