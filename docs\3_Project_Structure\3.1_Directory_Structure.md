# 3.1 项目目录结构

项目采用 Monorepo（单一代码库）结构，将 Chrome 插件和官方网站放在同一个仓库中进行管理，便于代码复用和统一维护。

## 完整目录结构

```
EchoAIExtention/
├── .github/                    # GitHub Actions (CI/CD) 配置
│   └── workflows/
│       └── ci.yml
├── docs/                       # 项目文档 (重构后)
│   ├── 1_Project_Research_and_Requirements/
│   ├── 2_Technology_Stack/
│   ├── 3_Project_Structure/
│   ├── 4_Development_Guide/
│   ├── 5_Project_Management/
│   └── 6_User_Manual/
├── extension/                  # 🔌 Chrome插件源码
│   ├── public/                 # 静态资源
│   │   ├── icons/              # 插件图标 (16, 32, 48, 128)
│   │   └── manifest.json       # 插件清单文件 (Manifest V3)
│   ├── src/                    # 源代码
│   │   ├── background/         # 后台脚本 (Service Worker)
│   │   ├── content/            # 内容脚本 (注入到AI网页)
│   │   ├── popup/              # 弹窗页面 (React应用)
│   │   ├── options/            # 选项页面 (React应用)
│   │   ├── components/         # 共享React组件 (如UI组件)
│   │   ├── lib/                # 公共库 (消息, 存储等)
│   │   ├── stores/             # 状态管理 (Zustand)
│   │   ├── types/              # TypeScript类型定义
│   │   ├── styles/             # 全局样式
│   │   └── setupTests.ts       # 测试环境配置
│   ├── dist/                   # Vite构建输出目录
│   ├── package.json            # 插件的依赖配置
│   ├── vite.config.ts          # Vite 构建配置
│   ├── tsconfig.json           # TypeScript 配置
│   └── tailwind.config.js      # Tailwind CSS 配置
├── website/                    # 🌐 官方网站源码 (Next.js)
│   ├── src/
│   │   ├── app/                # Next.js App Router 核心目录
│   │   ├── components/         # 网站共享的React组件
│   │   ├── lib/                # Supabase, Stripe等客户端
│   │   └── ...
│   ├── public/                 # 网站的静态资源
│   ├── package.json            # 网站的依赖配置
│   ├── next.config.js          # Next.js 配置
│   └── .env.example            # 环境变量模板
├── .gitignore                  # Git忽略文件配置
├── package.json                # Monorepo 根依赖和工作区脚本
└── README.md                   # 项目总述
```

## 核心目录职责

-   **`extension/`**: 独立完整的Chrome插件项目。
    -   `src/background`: 负责处理长期运行的后台任务和消息中转。
    -   `src/content`: 负责与AI聊天网站的DOM进行交互，是实现提示词捕获和注入的核心。
    -   `src/popup`: 用户点击插件图标时出现的弹窗界面。
    -   `src/options`: 插件的详细设置页面。
-   **`website/`**: 独立完整的Next.js全栈网站项目。
    -   `src/app`: 包含所有页面路由、API路由和布局。
    -   `src/lib`: 封装与第三方服务（如Supabase, Stripe）的交互逻辑。
-   **`docs/`**: 存放所有项目相关的设计、规划和开发文档。
-   **`.github/`**: 定义自动化工作流，如代码检查、测试和构建。
-   **根 `package.json`**: 定义整个项目的工作区（workspaces），并提供顶层命令（如 `npm run dev`）来同时操作插件和网站。
