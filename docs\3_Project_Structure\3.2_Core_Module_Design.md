# 3.2 核心模块设计

## 插件核心架构

EchoSync插件采用模块化的设计，确保各部分职责清晰，易于维护和扩展。

```mermaid
graph TD
    subgraph Browser
        subgraph AI_Chat_Page [AI聊天页面]
            Content_Script[Content Script]
        end
        Popup[Popup界面]
        Options[Options界面]
    end

    subgraph Extension_Process
        Background_SW[Background Service Worker]
        Storage[Chrome Storage API / IndexedDB]
    end

    Content_Script -- DOM操作 --> AI_Chat_Page
    Content_Script -- 消息通信 --> Background_SW
    Popup -- 消息通信/状态读取 --> Background_SW
    Options -- 消息通信/状态读取 --> Background_SW
    Background_SW -- 数据读写 --> Storage

    style AI_Chat_Page fill:#f9f,stroke:#333,stroke-width:2px
    style Popup fill:#ccf,stroke:#333,stroke-width:2px
    style Options fill:#ccf,stroke:#333,stroke-width:2px
```

### 1. Background Service Worker (`background/index.ts`)

作为插件的大脑，常驻后台运行。

-   **职责**:
    -   **消息中枢**: 接收并分发来自Content Script, Popup, Options页面的消息。
    -   **状态管理**: 维护全局状态，如当前激活的平台、同步开关等。
    -   **数据持久化**: 协调对 `chrome.storage` 或 `IndexedDB` 的数据写入和读取操作。
    -   **生命周期管理**: 处理插件的安装、更新等事件。

### 2. Content Scripts (`content/index.ts`)

注入到目标AI聊天网站的脚本，是功能实现的关键。

-   **职责**:
    -   **DOM交互**: 监听输入框、点击发送按钮、读取聊天记录等。
    -   **提示词捕获与注入**: 实时获取用户输入的提示词，或将其他页面的提示词填充到当前页面。
    -   **平台适配器调用**: 根据当前网站URL，动态加载并使用对应的AI平台适配器。

### 3. AI平台适配器 (`content/adapters/`)

为了应对不同AI网站的DOM结构差异，我们设计了适配器模式。

-   **基类 `AIAdapter`**: 定义所有适配器必须实现的统一接口。
    ```typescript
    // src/content/adapters/base.ts
    export abstract class AIAdapter {
      // 平台名称，如 'ChatGPT'
      abstract platformName: string;
      
      // 各平台关键元素的CSS选择器
      abstract selectors: {
        inputField: string;  // 输入框
        sendButton: string;  // 发送按钮
        messageContainer: string; // 消息列表容器
      };

      // 将提示词注入到输入框
      abstract injectPrompt(prompt: string): Promise<void>;
      
      // 从页面提取对话内容
      abstract extractConversation(): Promise<Conversation>;
      
      // 判断当前页面是否为该平台
      abstract isValidPage(): boolean;
    }
    ```
-   **具体实现**: 为每个支持的平台（ChatGPT, Gemini等）创建一个继承自 `AIAdapter` 的具体类，实现其抽象方法。
    ```typescript
    // src/content/adapters/chatgpt.ts
    export class ChatGPTAdapter extends AIAdapter {
      platformName = 'ChatGPT';
      selectors = {
        inputField: '#prompt-textarea',
        sendButton: '[data-testid="send-button"]',
        messageContainer: '[data-testid="conversation-turn"]'
      };
      // ... 实现具体方法
    }
    ```
-   **优势**: 这种设计使得**添加对新AI平台的支持变得非常简单**，只需创建一个新的适配器文件，而无需改动核心逻辑。

### 4. 通信与存储

-   **消息通信 (`lib/messaging.ts`)**: 封装了 `chrome.runtime.sendMessage` 和 `chrome.tabs.sendMessage`，并定义了统一的消息格式（类型、载荷），实现类型安全、可预测的跨模块通信。
-   **数据存储 (`lib/storage.ts`)**: 封装了 `chrome.storage.local` API，提供简单的 `get/set` 方法。未来对于大规模历史���录，会在此模块中集成 `IndexedDB`。
